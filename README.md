# scTimer 🧩

A modern, feature-rich speedcubing timer web application with comprehensive puzzle support, advanced statistics, and Stackmat timer integration.

## ✨ Features

### 🎯 Timer Modes

- **Normal Timer**: Classic spacebar-controlled timing with millisecond precision
- **Typing Mode**: Input times manually for offline solves
- **Stackmat Integration**: Professional timer support (desktop only)

### 🧩 Puzzle Support

Complete WCA event coverage with official scrambles:

- **2x2x2 - 7x7x7 Cubes** with official WCA scrambles
- **3x3x3 Variants**: Standard, One-Handed, Blindfolded, Fewest Moves
- **Other WCA Events**: Pyraminx, Skewb, Square-1, Clock, Megaminx
- **Multi-Blind**: Customizable attempt settings with multiple scrambles
- **2D Puzzle Visualization**: Visual representation for all supported puzzles

### 📊 Advanced Statistics

- **Real-time Averages**: ao5, ao12, ao50, ao100 with automatic calculation
- **Detailed Analytics**: Best times, session statistics, solve history
- **Performance Tracking**: Time trends and improvement analysis
- **Session Management**: Separate statistics per puzzle type

### 🌍 Multilingual Support

- **15+ Languages**: English, Spanish, French, German, Chinese, Japanese, and more
- **RTL Support**: Arabic, Hebrew, Kurdish Sorani with proper text direction
- **Dynamic Loading**: Language packs loaded on demand for performance

### 📱 Progressive Web App

- **Installable**: Add to home screen on mobile devices
- **Offline Support**: Works completely without internet connection
- **Responsive Design**: Optimized for all screen sizes and orientations
- **Touch Gestures**: Mobile-friendly controls and navigation

### ⚙️ Advanced Features

- **WCA Inspection Timer**: 15-second compliant inspection with audio cues
- **Penalty System**: +2 and DNF penalty management
- **Algorithm Database**: Comprehensive CFOP algorithm collection with OLL, PLL, and F2L cases
- **Solve History**: Complete solve log with filtering and search
- **Export/Import**: Backup and restore your data in JSON format
- **Customizable Settings**: Decimal precision, themes, audio preferences
- **FMC Mode**: Interactive solution input with move validation

### ⌨️ Keyboard Shortcuts

Comprehensive keyboard shortcuts for efficient navigation and control:

#### Timer Controls

- **SPACEBAR**: Start/stop timer
- **ESC**: Cancel inspection & close modals

#### Navigation & Actions

- **G**: Generate new scramble
- **L**: Toggle solve times list
- **S**: Open settings
- **T**: Open algorithm database (teaching/learning modal)
- **E**: Edit current scramble
- **C**: Copy scramble to clipboard
- **A**: Open detailed statistics panel

#### Display Toggles

- **V**: Toggle puzzle visualization
- **B**: Toggle statistics display
- **D**: Toggle dark mode
- **I**: Toggle WCA inspection

#### Penalty Management

- **CTRL+1**: Remove penalty from most recent solve
- **CTRL+2**: Add +2 penalty to most recent solve
- **CTRL+3**: Add DNF penalty to most recent solve

#### Event Switching

- **ALT+2-7**: Switch to 2×2×2 through 7×7×7 cubes
- **ALT+P**: Switch to Pyraminx
- **ALT+M**: Switch to Megaminx
- **ALT+C**: Switch to Clock
- **ALT+S**: Switch to Skewb
- **ALT+1**: Switch to Square-1
- **ALT+F**: Switch to 3×3×3 Fewest Moves
- **ALT+O**: Switch to 3×3×3 One-Handed

#### Blindfolded Events

- **ALT+CTRL+3**: Switch to 3×3×3 Blindfolded
- **ALT+CTRL+4**: Switch to 4×4×4 Blindfolded
- **ALT+CTRL+5**: Switch to 5×5×5 Blindfolded
- **ALT+CTRL+6**: Switch to 3×3×3 Multi-Blind

#### Session Management

- **ALT+N**: Create new session
- **CTRL+Q**: Empty current session (clear all times)
- **CTRL+X**: Export current session as JSON
- **Mobile Gestures**: Swipe controls for mobile devices

## 🚀 Quick Start

### Option 1: Direct Use

1. Open the web application in your browser
2. Start timing immediately - no installation required!

### Option 2: PWA Installation

1. Visit the web app in Chrome/Safari
2. Click "Install" when prompted
3. Use as a native app on your device

## 🎮 Usage Guide

### Basic Timer Operation

```
🔹 Hold SPACEBAR → Timer turns green → Release to start
🔹 Press SPACEBAR again → Stop timer
🔹 ESC → Cancel current solve
🔹 CTRL+Z → Delete last solve
🔹 Click Next (🔄) → Generate new scramble
```

### Stackmat Timer Setup

1. **Enable Stackmat Mode** in Settings
2. **Connect Y-splitter cable** to your computer's audio input
3. **Allow microphone access** when prompted
4. **Turn on Stackmat timer** - automatic detection and sync

> **Note**: Stackmat functionality requires desktop browsers with Web Audio API support. Mobile devices have limited compatibility due to hardware constraints.

### WCA Inspection Mode

1. **Enable "Use WCA Inspection (15s)"** in settings
2. **Press SPACEBAR once** → Start 15-second inspection
3. **Prepare your puzzle** during inspection
4. **Hold SPACEBAR** when ready → Timer turns green
5. **Release to start** → Begin solving

**WCA Inspection Rules:**

- ✅ **0-15 seconds**: Normal solve
- ⚠️ **15-17 seconds**: +2 penalty applied
- ❌ **17+ seconds**: DNF (Did Not Finish)

### FMC Mode

1. **Select FMC** from event dropdown
2. **Use virtual keyboard** to input solution moves
3. **Auto-validation** ensures legal move sequences
4. **Move counter** updates in real-time
5. **Submit solution** when complete

### Multi-Blind Solving

1. **Select "3x3x3 Multi-Blind"** from puzzle dropdown
2. **Enter number of cubes** to attempt (minimum 2)
3. **Generate scrambles** for all cubes
4. **View all scrambles** by clicking scramble area
5. **Start timer** when ready (inspection disabled for MBLD)
6. **Enter solved cubes** after stopping timer
7. **Result calculated** per WCA rules: (solved - (total - solved))

### Mobile Gestures

- **Swipe Down**: Delete recent solve
- **Swipe Up**: Add +2 penalty
- **Horizontal Swipes** (language-aware):
  - **RTL Languages**: Swipe left opens times panel, swipe right generates new scramble
  - **LTR Languages**: Swipe right opens times panel, swipe left generates new scramble
- **Long Press**: Access solve options

### Typing Mode

1. **Enable "Use Manual Input Timer"** in settings
2. **Enter times** in formats: `12.345`, `1:23.45`, `1:23:45.67`
3. **Press Enter** to submit time
4. **Inspection available** - press Space to start

### Keyboard Shortcuts

- **SPACEBAR**: Start/stop timer
- **ESC**: Cancel inspection or current solve
- **CTRL+Z**: Delete most recent solve
- **ENTER**: Submit time in typing mode

## 🛠️ Technical Details

### Browser Compatibility

| Feature      | Desktop                    | Mobile                        | Notes                  |
| ------------ | -------------------------- | ----------------------------- | ---------------------- |
| Basic Timer  | ✅ All browsers            | ✅ All browsers               | Full functionality     |
| Stackmat     | ✅ Chrome, Firefox, Safari | ❌ Limited                    | Web Audio API required |
| PWA Features | ✅ Modern browsers         | ✅ iOS Safari, Android Chrome | Offline support        |
| Audio Cues   | ✅ All browsers            | ✅ All browsers               | Inspection beeps       |

### Performance

- **Lightweight**: < 2MB total size
- **Fast Loading**: Critical resources prioritized
- **Efficient**: Minimal battery usage on mobile
- **Responsive**: 60fps animations and transitions

### Privacy & Data

- **Local Storage**: All data stored on your device
- **No Tracking**: No analytics or user tracking
- **Offline First**: Works completely offline
- **Export Control**: Full data ownership and portability

### Technologies Used

- **HTML5, CSS3, JavaScript (ES6+)**: Modern web standards
- **[cubing.js](https://js.cubing.net/cubing/)**: WCA-compliant scrambles and 2D visualization
- **[cubing-icons](https://icons.cubing.net/)**: Official WCA puzzle icons
- **[Stackmat Library](https://github.com/stilesdev/stackmat)**: Professional timer integration
- **Service Workers**: PWA offline functionality
- **Web Audio API**: Stackmat timer integration
- **LocalStorage**: Persistent data storage
- **Custom i18n**: Multi-language support with RTL layout

### Key Technologies

- **Vanilla JavaScript**: No framework dependencies
- **Web Audio API**: Stackmat timer integration
- **Service Workers**: PWA offline functionality
- **CSS Grid/Flexbox**: Responsive layouts
- **cubing.js**: Official WCA scramble generation

## 🎨 Color Palette

### Light Mode

| Color           | Hex Code  | Usage                         |
| --------------- | --------- | ----------------------------- |
| Background      | `#f5f5f5` | Main background               |
| Text            | `#333333` | Primary text                  |
| Container       | `#ffffff` | Card backgrounds              |
| Header          | `#a57865` | Header background             |
| Accent          | `#a57865` | Primary accent color          |
| Button          | `#8a6553` | Button background             |
| Button Hover    | `#755648` | Button hover state            |
| Ready State     | `#4caf50` | Timer ready (green)           |
| Running State   | `#e74c3c` | Timer running (red)           |
| Inspection      | `#f39c12` | Inspection countdown (orange) |
| Card Background | `#f9f9f9` | Secondary cards               |
| Border          | `#eeeeee` | Borders and dividers          |

### Dark Mode

| Color           | Hex Code  | Usage                         |
| --------------- | --------- | ----------------------------- |
| Background      | `#121212` | Main background               |
| Text            | `#f0f0f0` | Primary text                  |
| Container       | `#1e1e1e` | Card backgrounds              |
| Header          | `#6d4c41` | Header background             |
| Accent          | `#c69c84` | Primary accent color          |
| Button          | `#5d4037` | Button background             |
| Button Hover    | `#4e342e` | Button hover state            |
| Ready State     | `#66bb6a` | Timer ready (green)           |
| Running State   | `#ef5350` | Timer running (red)           |
| Inspection      | `#ffb74d` | Inspection countdown (orange) |
| Card Background | `#2d2d2d` | Secondary cards               |
| Border          | `#444444` | Borders and dividers          |

### FMC Mode Colors

| Color            | Hex Code              | Usage              |
| ---------------- | --------------------- | ------------------ |
| Submit Button    | `#66bb6a` / `#4caf50` | Submit solution    |
| Case Button      | `#9575cd` / `#7e57c2` | Case conversion    |
| Backspace        | `#ef9a9a` / `#e57373` | Delete/backspace   |
| Space Button     | `#90a4ae` / `#607d8b` | Space key          |
| Visualization    | `#7986cb` / `#5c6bc0` | 3D visualization   |
| Valid Solution   | `#27ae60`             | Valid FMC result   |
| Invalid Solution | `#e74c3c`             | DNF/Invalid result |

## 📄 License

This project is proprietary software. All rights reserved.

## 🙏 Acknowledgments

- **cubing.js** - Official WCA scramble generation
- **Stackmat Library** - Professional timer integration
- **World Cube Association** - Official regulations and scramble algorithms
- **Speedcubing Community** - Inspiration and feedback

---

**Happy Cubing! 🧩✨**
