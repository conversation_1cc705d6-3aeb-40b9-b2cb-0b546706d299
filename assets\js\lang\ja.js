// Japanese language file
export default {
  dir: "ltr",
  settings: {
    title: "設定",
    save: "保存",
    close: "閉じる",
    language: "言語",
  },
  info: {
    title: "情報",
    pwaInstall: {
      title: "アプリとしてインストール",
      description:
        "最高の体験のためにscTimerをプログレッシブWebアプリとしてインストールしてください。オフラインで動作し、ネイティブアプリのように感じられます。",
      install: "アプリをインストール",
      iosTitle: "iOS/iPadインストール：",
      iosStep1: "1. 共有ボタンをタップ",
      iosStep2: "2. 下にスクロールして「ホーム画面に追加」をタップ",
      iosStep3: "3. 「追加」をタップしてインストール",
      note: "Chrome、Safari、その他のモダンブラウザで利用可能",
    },
    shortcuts: {
      title: "キーボードショートカット",
      timer: "タイマーコントロール",
      spacebar: "タイマー開始/停止",
      escape: "インスペクションをキャンセルしてモーダルを閉じる",
      navigation: "ナビゲーションとアクション",
      generate: "新しいスクランブルを生成",
      list: "タイムリストを切り替え",
      settings: "設定を開く",
      edit: "現在のスクランブルを編集",
      copy: "スクランブルをクリップボードにコピー",
      stats: "詳細統計を開く",
      display: "表示を切り替え",
      visualization: "パズル可視化を切り替え",
      statistics: "統計表示を切り替え",
      darkMode: "ダークモードを切り替え",
      inspection: "WCAインスペクションを切り替え",
      learning: "アルゴリズムデータベースを開く",
      penalties: "ペナルティ管理",
      removePenalty: "最近のソルブからペナルティを削除",
      addPlus2: "最近のソルブに+2ペナルティを追加",
      addDNF: "最近のソルブにDNFペナルティを追加",
      session: "セッション管理",
      emptySession: "現在のセッションを空にする",
      exportSession: "現在のセッションをエクスポート",
      eventSwitching: "イベント切り替え",
      alt2to7: "2×2×2から7×7×7キューブに切り替え",
      altP: "ピラミンクスに切り替え",
      altM: "メガミンクスに切り替え",
      altC: "クロックに切り替え",
      altS: "スキューブに切り替え",
      alt1: "スクエア1に切り替え",
      altF: "3×3×3最少手数に切り替え",
      altO: "3×3×3片手に切り替え",
      blindfolded: "目隠しイベント",
      altCtrl3: "3×3×3目隠しに切り替え",
      altCtrl4: "4×4×4目隠しに切り替え",
      altCtrl5: "5×5×5目隠しに切り替え",
      altCtrl6: "3×3×3マルチ目隠しに切り替え",
      sessionMgmt: "セッション管理",
      altN: "新しいセッションを作成",
    },
    gestures: {
      title: "モバイルジェスチャー",
      swipeDown: "下にスワイプ",
      swipeDownDesc: "最近のソルブを削除",
      swipeUp: "上にスワイプ",
      swipeUpDesc: "ペナルティを循環（なし/+2/DNF）",
      swipeLeft: "左にスワイプ",
      swipeLeftDesc: "LTR：新しいスクランブル | RTL：タイムリスト",
      swipeRight: "右にスワイプ",
      swipeRightDesc: "LTR：タイムリスト | RTL：新しいスクランブル",
      doubleClick: "ダブルクリック",
      doubleClickDesc: "現在のスクランブルをコピー（PC/モバイル）",
      longPress: "長押し/クリック&ホールド",
      longPressDesc: "現在のスクランブルを編集（PC/モバイル）",
    },
    learning: {
      title: "アルゴリズムデータベース",
      description: "スピードキューブのアルゴリズムを学習・練習",
      puzzle: "パズル",
      method: "メソッド",
      step: "ステップ",
      case: "ケース",
      algorithm: "アルゴリズム",
      alternatives: "代替アルゴリズム",
      difficulty: "難易度",
      moveCount: "手数",
      category: "カテゴリ",
      source: "ソース",
      noAlgorithms: "この選択に利用可能なアルゴリズムがありません",
      loadingError: "アルゴリズムデータベースの読み込みエラー",
    },
    features: {
      title: "主な機能",
      timer: "プロフェッショナルタイマー",
      timerDesc: "インスペクションモード付きWCA準拠タイミング",
      puzzles: "すべてのWCAイベント",
      puzzlesDesc: "すべての公式WCAパズルイベントの完全サポート",
      statistics: "高度な統計",
      statisticsDesc: "ao5、ao12、ao100を含む詳細分析",
      scrambles: "公式スクランブル",
      scramblesDesc: "2D可視化付きWCA標準スクランブル生成",
      multilingual: "多言語サポート",
      multilingualDesc: "RTLサポート付き15+言語",
      sync: "Google Drive同期",
      syncDesc: "スマートマージ付きクロスデバイス同期",
    },
    sync: {
      title: "Google Drive同期",
      description:
        "Google Driveを使用してすべてのデバイスでソルブタイムを同期します。データは個人のGoogle Driveアカウントに安全に保存されます。",
      secure: "安全でプライベート",
      automatic: "自動同期",
      offline: "オフラインサポート",
      smartMerge: "スマートマージ",
      note: "設定でGoogle Drive同期を有効にして、すべてのデバイスでタイムを同期させてください。",
      status: "ステータス：",
      notConnected: "未接続",
      connected: "接続済み",
      connect: "接続",
      disconnect: "切断",
      upload: "Driveにアップロード",
      download: "Driveからダウンロード",
      autoSync: "自動同期",
      autoSyncNote:
        "インターネットに接続されているときに自動的にタイムを同期します",
      uploading: "アップロード中...",
      downloading: "ダウンロード中...",
      syncing: "同期中...",
      uploadSuccess: "アップロード成功",
      downloadSuccess: "ダウンロード成功",
      uploadFailed: "アップロード失敗",
      downloadFailed: "ダウンロード失敗",
      uploadConfirm:
        "ローカルタイムをGoogle Driveにアップロードしてマージしますか？",
      downloadConfirm:
        "Google Driveからデータをダウンロードしてローカルタイムとマージしますか？",
      downloadMergeConfirm:
        "Google Driveのデータをローカルタイムとマージします。続行しますか？",
      reloadConfirm: "変更を確認するためにページを再読み込みしますか？",
      autoSyncEnabled: "自動同期が有効になりました",
      signInFailed: "サインインに失敗しました",
      noSyncFile: "同期ファイルが見つかりません",
      noDataFound: "データが見つかりません",
      uploadCancelled: "アップロードがキャンセルされました",
      downloadCancelled: "ダウンロードがキャンセルされました",
      syncSuccessful: "同期成功",
      syncFailed: "同期失敗",
      error: "エラー",
    },
  },
  timerOptions: {
    title: "タイマーオプション",
    warningSounds: "警告音を有効にする",
    useInspection: "WCAインスペクションを使用（15秒）",
    inspectionSound: "インスペクション音：",
    inspectionSoundNone: "なし",
    inspectionSoundVoice: "音声",
    inspectionSoundBeep: "ビープ",
    stackmatResetInspection: "Stackmatリセットでインスペクション開始",
    stackmatResetNote: "注意：タイマーが0.000でない時のみ動作",
    inputTimer: "入力タイマーモード（手動でタイムを入力）",
    timerMode: "タイマーモード：",
    timerModeTimer: "タイマー",
    timerModeTyping: "入力",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "Bluetooth（近日公開）",
    microphoneInput: "マイク入力",
    microphoneAuto: "自動検出",
    microphoneNote: "Yスプリッターまたは外部マイクを選択",
    decimalPlaces: "小数点以下桁数：",
    decimalPlacesNone: "なし（12）",
    decimalPlaces1: "1桁（12.3）",
    decimalPlaces2: "2桁（12.34）",
    decimalPlaces3: "3桁（12.345）",
  },
  displayOptions: {
    title: "表示オプション",
    showVisualization: "パズル可視化を表示",
    showStats: "統計を表示",
    showDebug: "デバッグ情報を表示",
    darkMode: "ダークモード",
    showFMCKeyboard: "FMCキーボードを表示",
    scrambleFontSize: "スクランブルフォントサイズ",
  },
  app: {
    title: "scTimer",
    description: "WCAインスペクションと統計付きスピードキューブタイマー",
    enterTime: "タイムを入力",
    enterSolveTime: "ソルブタイムを手動入力",
    generateScrambles: "スクランブルを生成",
    outOf: "総数：",
    numberOfCubes: "キューブ数（最低2個）：",
    numberOfCubesSolved: "解けたキューブ数：",
  },
  timer: {
    ready: "準備完了",
    running: "実行中",
    idle: "待機中",
    inspection: "インスペクション",
    holding: "保持中",
  },
  stats: {
    title: "統計",
    best: "ベスト",
    worst: "ワースト",
    mean: "平均",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "ベストmo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "ソルブ",
    attempts: "試行",
    moreStats: "詳細統計",
  },
  statsDetails: {
    title: "統計詳細",
    titleFor: "統計詳細",
    overview: "概要",
    averages: "平均",
    records: "記録",
    timeDistribution: "タイム分布",
    progressChart: "進捗チャート",
    sessionAnalysis: "セッション分析",
    predictions: "予測",
    standardDeviation: "標準偏差",
    bestSingle: "ベストシングル",
    bestAo5: "ベストao5",
    bestAo12: "ベストao12",
    bestAo100: "ベストao100",
    bestAo1000: "ベストao1000",
    totalTime: "総時間",
    averageTime: "平均時間",
    solvesPerHour: "1時間あたりソルブ数",
    consistency: "一貫性",
    nextAo5: "次のao5目標",
    nextAo12: "次のao12目標",
    improvementRate: "改善率",
    targetTime: "目標時間",
    currentSession: "現在のセッション",
    allSessions: "すべてのセッション",
    importTimes: "タイムをインポート",
    exportJSON: "JSONエクスポート",
    exportCSV: "CSVエクスポート",
  },
  solveDetails: {
    title: "ソルブ詳細",
    time: "タイム",
    date: "日付",
    scramble: "スクランブル",
    editedScramble: "編集されたスクランブル",
    copyScramble: "スクランブルをコピー",
    penalty: "ペナルティ",
    none: "なし",
    comment: "コメント",
    addComment: "コメントを追加...",
    save: "保存",
    share: "共有",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "スクランブルをコピーしました",
    noSolvesToDelete: "削除するソルブがありません",
    solveDeleted: "ソルブを削除しました",
    cannotAddPenaltyMBLD: "MBLDソルブにペナルティを追加できません",
    dnfRemoved: "DNFを削除しました",
    dnfAdded: "DNFを追加しました",
    plus2Added: "+2ペナルティを追加しました",
    penaltyRemoved: "ペナルティを削除しました",
    newScrambleGenerated: "新しいスクランブルを生成しました",
    timesPanelOpened: "タイムパネルを開きました",
  },
  times: {
    title: "ソルブタイム",
    clear: "タイムをクリア",
    close: "閉じる",
    delete: "タイムを削除",
    confirmClear: "このイベントのすべてのタイムをクリアしてもよろしいですか？",
    confirmDelete: "このタイムを削除してもよろしいですか？",
  },
  buttons: {
    viewTimes: "タイムを表示",
    ok: "OK",
    cancel: "キャンセル",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3目隠し",
    "333fm": "3×3×3最少手数",
    "333oh": "3×3×3片手",
    clock: "クロック",
    minx: "メガミンクス",
    pyram: "ピラミンクス",
    skewb: "スキューブ",
    sq1: "スクエア1",
    "444bf": "4×4×4目隠し",
    "555bf": "5×5×5目隠し",
    "333mbf": "3×3×3マルチ目隠し",
  },
  mbld: {
    cubeCount: "キューブ",
    solvedCount: "解けたキューブ",
    totalCount: "総キューブ数",
    totalCubes: "総キューブ数",
    cubesSolved: "解けたキューブ",
    bestPoints: "ベストポイント",
    successRate: "成功率",
    points: "ポイント",
    save: "結果を保存",
    visualizations: "マルチ目隠し可視化",
    scrambles: "マルチ目隠しスクランブル",
    enterValidNumber: "有効な解けたキューブ数を入力してください。",
    noScrambles:
      "MBLDスクランブルがありません。まず3×3×3マルチ目隠しイベントを選択してください。",
    visualizationNotFound:
      "可視化モーダルが見つかりません。ページを更新して再試行してください。",
    containerNotFound:
      "可視化コンテナが見つかりません。ページを更新して再試行してください。",
    clickToView: "すべてのキューブ可視化とスクランブルを表示するにはクリック",
    bestScore: "ベストスコア",
    worstScore: "ワーストスコア",
    meanScore: "平均スコア",
    averageScore: "平均スコア",
    attempts: "試行",
    totalAttempts: "総試行数",
    clickToViewScrambles: "すべてのスクランブルを表示するにはクリック",
    clickToViewScramblesCount: "すべての{0}スクランブルを表示するにはクリック",
    setup: "マルチ目隠しセットアップ",
    results: "マルチ目隠し結果",
    generateScrambles: "スクランブルを生成",
    saveResult: "結果を保存",
    cubeNumber: "キューブ",
    numberOfCubesMinimum: "キューブ数（最低2個）：",
    numberOfCubesSolved: "解けたキューブ数：",
    saveFirst: "まず結果を保存してください。",
    visualizationsTitle: "マルチ目隠し可視化（{0}キューブ）",
    timeLimit: "制限時間：{0}分",
    timeLimitExceeded: "制限時間を超過しました。結果はDNFになります。",
    negativePoints: "マイナスポイント。結果はDNFになります。",
  },
  modals: {
    error: "エラー",
    warning: "警告",
    info: "情報",
    confirm: "確認",
    prompt: "入力が必要",
  },
  stackmat: {
    error: "Stackmatエラー",
    noMicrophone:
      "Stackmatタイマーの開始に失敗：マイクが見つかりません。マイクを接続して再試行してください。",
    connected: "接続済み",
    disconnected: "切断済み",
    settingUp: "設定中...",
  },
  sessions: {
    newSessionTitle: "新しいセッション",
    editSessionTitle: "セッションを編集",
    sessionName: "セッション名：",
    sessionNamePlaceholder: "マイセッション",
    puzzleType: "パズルタイプ：",
    create: "作成",
    save: "保存",
  },
  scramble: {
    loading: "スクランブル読み込み中...",
  },
  debug: {
    timerState: "タイマー状態：",
    spaceHeldFor: "スペース保持時間：",
    currentEvent: "現在のイベント：",
    scrambleSource: "スクランブルソース：",
  },
  fmc: {
    title: "最少手数チャレンジ",
    info: "可能な限り少ない手数でキューブを解いてください。解法を見つけるのに60分あります。",
    timeRemaining: "残り時間：",
    scramble: "スクランブル：",
    solution: "解法：",
    moveCount: "手数：",
    moves: "手",
    submit: "提出",
    resultTitle: "FMC結果",
    resultTime: "時間：",
    resultSolution: "解法：",
    resultOk: "OK",
    solutionPlaceholder: "標準WCA記法を使用してここに解法を入力...",
    notationHelp: "記法ヘルプ：",
    notationHelpContent:
      "面回転：U、D、L、R、F、B（'または2接尾辞付き）<br>ワイド回転：Uw、Dw等<br>スライス回転：M、E、S<br>回転：x、y、z（総手数にカウントされません）",
    submitSolution: "解法を提出",
    validSolution: "有効な解法",
    invalidNotation: "無効な記法が検出されました",
    bestMoves: "ベスト手数",
    worstMoves: "ワースト手数",
    meanMoves: "平均手数",
    bestMo3: "ベストmo3",
    averageMoves: "平均手数",
    attempts: "試行",
    totalAttempts: "総試行数",
    tooManyMoves: "解法が80手制限を超えています",
    timeExceeded:
      "制限時間を超過しました。提出しない場合、解法はDNFとしてマークされます。",
    confirmClose: "閉じてもよろしいですか？試行はDNFとしてマークされます。",
    dnfReasonTimeout: "制限時間超過",
    dnfReasonInvalid: "無効な記法",
    dnfReasonTooManyMoves: "解法が80手を超過",
    dnfReasonAbandoned: "試行を放棄",
    confirmSubmit: "解法を提出してもよろしいですか？",
    pressToStart: "スペースキーを押してFMC試行を開始",
    solutionAccepted: "解法が受け入れられました",
    clickToViewTwizzle: "下のリンクをクリックしてTwizzleで解法を表示",
    viewOnTwizzle: "Twizzleで表示",
    moveCountLabel: "手数：",
    movesHTM: "手（HTM）",
    timeUsedLabel: "使用時間：",
    loadingFMC: "FMC読み込み中",
    generatingScramble: "スクランブル生成とインターフェース準備中",
  },
  tutorial: {
    welcomeTitle: "scTimerへようこそ！",
    welcomeSubtitle: "あなたのプロフェッショナルスピードキューブタイマー",
    selectLanguage: "言語を選択：",
    feature1: "WCA標準タイマー",
    feature2: "高度な統計",
    feature3: "すべてのWCAイベント",
    feature4: "スクランブルジェネレーター",
    welcomeDescription:
      "scTimerを効果的に使用する方法を学ぶクイックツアーはいかがですか？チュートリアルでは、わずか数ステップで主要機能をご案内します。",
    skipTutorial: "チュートリアルをスキップ",
    startTour: "ツアーを開始",
    step1: {
      title: "スクランブル表示",
      text: "現在のパズルのスクランブルシーケンスを表示します。各スクランブルはWCA標準に従ってランダムに生成されます。",
    },
    step2: {
      title: "タイマーコントロール",
      text: "スペースバーを押し続けてタイミングを開始し、離して解き始めます。モバイルでは、タイマーエリアをタップ&ホールドします。タイマーはWCAインスペクション標準に従います。",
    },
    step3: {
      title: "イベントセレクター",
      text: "3x3x3、2x2x2、4x4x4など、すべてのWCAイベントから選択できます。クリックまたはタップしてドロップダウンメニューを開きます。",
    },
    step4: {
      title: "統計追跡",
      text: "ベストタイム、5回、12回、100回の平均を含む詳細統計で進捗を追跡します。任意の統計をクリックして詳細を表示します。",
    },
    step5: {
      title: "新しいスクランブルを生成",
      text: "次のソルブの準備ができたら新しいスクランブルを生成します。キーボードショートカット：Nを押すかシャッフルアイコンをクリック。",
    },
    step6: {
      title: "設定とカスタマイズ",
      text: "インスペクション時間、サウンドオプション、タイマーモード、表示設定でタイマー体験をカスタマイズします。キーボードショートカット：Sを押す。",
    },
    step7: {
      title: "キーボードショートカット",
      text: "これらのショートカットをマスター：スペースバー（タイマー開始/停止）、N（新しいスクランブル）、S（設定）、ESC（モーダルを閉じる）、矢印キー（ナビゲート）。モバイルではスワイプジェスチャーを使用！",
    },
    step8: {
      title: "モバイルジェスチャー",
      text: "モバイルデバイスで：左にスワイプしてタイムパネルを開く、右にスワイプして閉じる、タイマーをタップ&ホールドして開始、スクランブルをダブルタップしてコピー。可視化でピンチしてズーム。",
    },
    step9: {
      title: "プロのヒントと機能",
      text: "WCA練習のために設定でインスペクション時間を有効にします。さまざまなイベントを追跡するために異なるセッションを使用します。分析のためにタイムをエクスポートします。タイマーはPWAとしてオフラインで動作します！",
    },
    previous: "前へ",
    next: "次へ",
    finish: "完了",
    close: "閉じる",
    stepCounter: "の",
    restartTutorial: "チュートリアルを再開",
  },
};
