// Finnish language file
export default {
  dir: "ltr",
  settings: {
    title: "Asetukset",
    save: "<PERSON><PERSON><PERSON>",
    close: "<PERSON>je",
    language: "<PERSON><PERSON>",
  },
  info: {
    title: "<PERSON><PERSON><PERSON>",
    pwaInstall: {
      title: "<PERSON><PERSON><PERSON>",
      description:
        "<PERSON><PERSON><PERSON> scTimer Progressive Web App -sovelluksena parasta kokemusta varten. Toimii offline-tilassa ja tuntuu natiivisovellukselta.",
      install: "Asenna Sovellus",
      iosTitle: "iOS/iPad Asennus:",
      iosStep1: "1. Napauta Jaa-painiketta",
      iosStep2: '2. <PERSON>ieri<PERSON><PERSON> alas ja napauta "Lisää kotinäytölle"',
      iosStep3: '3. Na<PERSON><PERSON> "Lisää" asentaaksesi',
      note: "Saatavilla Chromessa, Safarissa ja muissa moderneissa selaimissa",
    },
    shortcuts: {
      title: "Näppäinoikotiet",
      timer: "Ajastimen Ohjaimet",
      spacebar: "<PERSON><PERSON>ynnistä/pys<PERSON><PERSON><PERSON> a<PERSON>",
      escape: "<PERSON>uta tarkastus ja sulje modaalit",
      navigation: "Navigointi ja Toiminnot",
      generate: "Luo uusi sekoitus",
      list: "Vaihda aikojen lista",
      settings: "Avaa asetukset",
      edit: "Muokkaa nykyistä sekoitusta",
      copy: "Kopioi sekoitus leikepöydälle",
      stats: "Avaa yksityiskohtaiset tilastot",
      display: "Vaihda Näyttö",
      visualization: "Vaihda palapelin visualisointi",
      statistics: "Vaihda tilastojen näyttö",
      darkMode: "Vaihda tumma tila",
      inspection: "Vaihda WCA tarkastus",
      learning: "Avaa algoritmi tietokanta",
      penalties: "Rangaistusten Hallinta",
      removePenalty: "Poista rangaistus viimeisestä ratkaisusta",
      addPlus2: "Lisää +2 rangaistus viimeiseen ratkaisuun",
      addDNF: "Lisää DNF rangaistus viimeiseen ratkaisuun",
      session: "Istunnon Hallinta",
      emptySession: "Tyhjennä nykyinen istunto",
      exportSession: "Vie nykyinen istunto",
      eventSwitching: "Tapahtuman Vaihto",
      alt2to7: "Vaihda 2×2×2 - 7×7×7 kuutioihin",
      altP: "Vaihda Pyraminxiin",
      altM: "Vaihda Megaminxiin",
      altC: "Vaihda Kelloon",
      altS: "Vaihda Skewbiin",
      alt1: "Vaihda Square-1:een",
      altF: "Vaihda 3×3×3 Vähiten Siirtoja",
      altO: "Vaihda 3×3×3 Yksi Käsi",
      blindfolded: "Sokkoratkaisutapahtumat",
      altCtrl3: "Vaihda 3×3×3 Sokkoratkaisuun",
      altCtrl4: "Vaihda 4×4×4 Sokkoratkaisuun",
      altCtrl5: "Vaihda 5×5×5 Sokkoratkaisuun",
      altCtrl6: "Vaihda 3×3×3 Multi-Sokkoratkaisuun",
      sessionMgmt: "Istunnon Hallinta",
      altN: "Luo uusi istunto",
    },
    gestures: {
      title: "Mobiilieleet",
      swipeDown: "Pyyhkäise Alas",
      swipeDownDesc: "Poista viimeisin ratkaisu",
      swipeUp: "Pyyhkäise Ylös",
      swipeUpDesc: "Vaihda rangaistuksia (ei mitään/+2/DNF)",
      swipeLeft: "Pyyhkäise Vasemmalle",
      swipeLeftDesc: "LTR: Uusi sekoitus | RTL: Aikojen lista",
      swipeRight: "Pyyhkäise Oikealle",
      swipeRightDesc: "LTR: Aikojen lista | RTL: Uusi sekoitus",
      doubleClick: "Kaksoisklikkaus",
      doubleClickDesc: "Kopioi nykyinen sekoitus (PC/Mobiili)",
      longPress: "Pitkä Painallus/Klikkaa ja Pidä",
      longPressDesc: "Muokkaa nykyistä sekoitusta (PC/Mobiili)",
    },
    learning: {
      title: "Algoritmi Tietokanta",
      description: "Opi ja harjoittele speedcubing-algoritmeja",
      puzzle: "Palapeli",
      method: "Menetelmä",
      step: "Vaihe",
      case: "Tapaus",
      algorithm: "Algoritmi",
      alternatives: "Vaihtoehtoiset Algoritmit",
      difficulty: "Vaikeus",
      moveCount: "Siirtojen Määrä",
      category: "Kategoria",
      source: "Lähde",
      noAlgorithms: "Ei algoritmeja saatavilla tälle valinnalle",
      loadingError: "Virhe algoritmi tietokannan lataamisessa",
    },
    features: {
      title: "Pääominaisuudet",
      timer: "Ammattimainen Ajastin",
      timerDesc: "WCA-yhteensopiva ajanotto tarkastustilalla",
      puzzles: "Kaikki WCA Tapahtumat",
      puzzlesDesc:
        "Täydellinen tuki kaikille virallisille WCA palapelitapahtumille",
      statistics: "Edistyneet Tilastot",
      statisticsDesc: "Yksityiskohtaiset analyysit ao5, ao12, ao100 kanssa",
      scrambles: "Viralliset Sekoitukset",
      scramblesDesc: "WCA-standardi sekoitusten luonti 2D visualisoinnilla",
      multilingual: "Monikielinen Tuki",
      multilingualDesc: "15+ kieltä RTL-tuella",
      sync: "Google Drive Synkronointi",
      syncDesc: "Laitteiden välinen synkronointi älykkäällä yhdistämisellä",
    },
    sync: {
      title: "Google Drive Synkronointi",
      description:
        "Synkronoi ratkaisuaikasi kaikilla laitteilla Google Driven avulla. Tietosi tallennetaan turvallisesti henkilökohtaiselle Google Drive -tilillesi.",
      secure: "Turvallinen ja Yksityinen",
      automatic: "Automaattinen Synkronointi",
      offline: "Offline-tuki",
      smartMerge: "Älykäs Yhdistäminen",
      note: "Ota Google Drive synkronointi käyttöön Asetuksissa pitääksesi aikasi synkronoituina kaikilla laitteillasi.",
      status: "Tila:",
      notConnected: "Ei yhteyttä",
      connected: "Yhdistetty",
      connect: "Yhdistä",
      disconnect: "Katkaise yhteys",
      upload: "Lataa Driveen",
      download: "Lataa Drivesta",
      autoSync: "Automaattinen Synkronointi",
      autoSyncNote:
        "Synkronoi aikasi automaattisesti kun olet yhteydessä internetiin",
      uploading: "Ladataan...",
      downloading: "Ladataan...",
      syncing: "Synkronoidaan...",
      uploadSuccess: "Lataus onnistui",
      downloadSuccess: "Lataus onnistui",
      uploadFailed: "Lataus epäonnistui",
      downloadFailed: "Lataus epäonnistui",
      uploadConfirm: "Lataa ja yhdistä paikalliset aikasi Google Driveen?",
      downloadConfirm:
        "Lataa ja yhdistä tiedot Google Drivesta paikallisiin aikoihisi?",
      downloadMergeConfirm:
        "Tämä yhdistää Google Drive tiedot paikallisiin aikoihisi. Jatka?",
      reloadConfirm: "Lataa sivu uudelleen nähdäksesi muutokset?",
      autoSyncEnabled: "Automaattinen synkronointi käytössä",
      signInFailed: "Kirjautuminen epäonnistui",
      noSyncFile: "Synkronointitiedostoa ei löytynyt",
      noDataFound: "Tietoja ei löytynyt",
      uploadCancelled: "Lataus peruutettu",
      downloadCancelled: "Lataus peruutettu",
      syncSuccessful: "Synkronointi onnistui",
      syncFailed: "Synkronointi epäonnistui",
      error: "Virhe",
    },
  },
  timerOptions: {
    title: "Ajastimen Asetukset",
    warningSounds: "Ota Varoitusäänet Käyttöön",
    useInspection: "Käytä WCA Tarkastusta (15s)",
    inspectionSound: "Tarkastusääni:",
    inspectionSoundNone: "Ei mitään",
    inspectionSoundVoice: "Ääni",
    inspectionSoundBeep: "Piippaus",
    stackmatResetInspection: "Stackmat Nollaus Aktivoi Tarkastuksen",
    stackmatResetNote: "Huomautus: Toimii vain kun ajastin ei ole 0.000:ssa",
    inputTimer: "Syöttöajastin Tila (Syötä ajat manuaalisesti)",
    timerMode: "Ajastimen Tila:",
    timerModeTimer: "Ajastin",
    timerModeTyping: "Kirjoittaminen",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "Bluetooth (Tulossa Pian)",
    microphoneInput: "Mikrofonin Syöttö",
    microphoneAuto: "Automaattinen tunnistus",
    microphoneNote: "Valitse Y-jakajasi tai ulkoinen mikrofoni",
    decimalPlaces: "Desimaalipaikat:",
    decimalPlacesNone: "Ei mitään (12)",
    decimalPlaces1: "1 (12.3)",
    decimalPlaces2: "2 (12.34)",
    decimalPlaces3: "3 (12.345)",
  },
  displayOptions: {
    title: "Näyttöasetukset",
    showVisualization: "Näytä Palapelin Visualisointi",
    showStats: "Näytä Tilastot",
    showDebug: "Näytä Virheenkorjaustiedot",
    darkMode: "Tumma Tila",
    showFMCKeyboard: "Näytä FMC Näppäimistö",
    scrambleFontSize: "Sekoituksen Fonttikoko",
  },
  app: {
    title: "scTimer",
    description: "Speedcubing ajastin WCA tarkastuksella ja tilastoilla",
    enterTime: "Syötä aika",
    enterSolveTime: "Syötä ratkaisuaika manuaalisesti",
    generateScrambles: "Luo Sekoituksia",
    outOf: "Yhteensä:",
    numberOfCubes: "Kuutioiden määrä (vähintään 2):",
    numberOfCubesSolved: "Ratkaistujen kuutioiden määrä:",
  },
  timer: {
    ready: "Valmis",
    running: "Käynnissä",
    idle: "Toimeton",
    inspection: "Tarkastus",
    holding: "Pidossa",
  },
  stats: {
    title: "Tilastot",
    best: "Paras",
    worst: "Huonoin",
    mean: "Keskiarvo",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "Paras mo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "Ratkaisut",
    attempts: "Yritykset",
    moreStats: "Lisää Tilastoja",
  },
  statsDetails: {
    title: "Tilastojen Yksityiskohdat",
    titleFor: "Tilastojen Yksityiskohdat",
    overview: "Yleiskatsaus",
    averages: "Keskiarvot",
    records: "Ennätykset",
    timeDistribution: "Aikojen Jakauma",
    progressChart: "Edistymiskaavio",
    sessionAnalysis: "Istunnon Analyysi",
    predictions: "Ennusteet",
    standardDeviation: "Keskihajonta",
    bestSingle: "Paras Yksittäinen",
    bestAo5: "Paras ao5",
    bestAo12: "Paras ao12",
    bestAo100: "Paras ao100",
    bestAo1000: "Paras ao1000",
    totalTime: "Kokonaisaika",
    averageTime: "Keskimääräinen Aika",
    solvesPerHour: "Ratkaisuja/Tunti",
    consistency: "Johdonmukaisuus",
    nextAo5: "Seuraava ao5 Tavoite",
    nextAo12: "Seuraava ao12 Tavoite",
    improvementRate: "Parannusvauhti",
    targetTime: "Tavoiteaika",
    currentSession: "Nykyinen Istunto",
    allSessions: "Kaikki Istunnot",
    importTimes: "Tuo Ajat",
    exportJSON: "Vie JSON",
    exportCSV: "Vie CSV",
  },
  solveDetails: {
    title: "Ratkaisun Yksityiskohdat",
    time: "Aika",
    date: "Päivämäärä",
    scramble: "Sekoitus",
    editedScramble: "Muokattu Sekoitus",
    copyScramble: "Kopioi sekoitus",
    penalty: "Rangaistus",
    none: "Ei mitään",
    comment: "Kommentti",
    addComment: "Lisää kommentti...",
    save: "Tallenna",
    share: "Jaa",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "Sekoitus kopioitu",
    noSolvesToDelete: "Ei ratkaisuja poistettavaksi",
    solveDeleted: "Ratkaisu poistettu",
    cannotAddPenaltyMBLD: "MBLD ratkaisuun ei voi lisätä rangaistusta",
    dnfRemoved: "DNF poistettu",
    dnfAdded: "DNF lisätty",
    plus2Added: "+2 rangaistus lisätty",
    penaltyRemoved: "Rangaistus poistettu",
    newScrambleGenerated: "Uusi sekoitus luotu",
    timesPanelOpened: "Aikapaneeli avattu",
  },
  times: {
    title: "Ratkaisuajat",
    clear: "Tyhjennä Ajat",
    close: "Sulje",
    delete: "Poista aika",
    confirmClear:
      "Oletko varma, että haluat tyhjentää kaikki ajat tälle tapahtumalle?",
    confirmDelete: "Oletko varma, että haluat poistaa tämän ajan?",
  },
  buttons: {
    viewTimes: "Näytä Ajat",
    ok: "OK",
    cancel: "Peruuta",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3 Sokkoratkaisu",
    "333fm": "3×3×3 Vähiten Siirtoja",
    "333oh": "3×3×3 Yksi Käsi",
    clock: "Kello",
    minx: "Megaminx",
    pyram: "Pyraminx",
    skewb: "Skewb",
    sq1: "Square-1",
    "444bf": "4×4×4 Sokkoratkaisu",
    "555bf": "5×5×5 Sokkoratkaisu",
    "333mbf": "3×3×3 Multi-Sokkoratkaisu",
  },
  mbld: {
    cubeCount: "Kuutiot",
    solvedCount: "Ratkaistut Kuutiot",
    totalCount: "Kuutioita Yhteensä",
    totalCubes: "Kuutioita Yhteensä",
    cubesSolved: "Ratkaistut Kuutiot",
    bestPoints: "Parhaat Pisteet",
    successRate: "Onnistumisprosentti",
    points: "pistettä",
    save: "Tallenna Tulos",
    visualizations: "Multi-Sokkoratkaisu Visualisoinnit",
    scrambles: "Multi-Sokkoratkaisu Sekoitukset",
    enterValidNumber: "Syötä kelvollinen ratkaistujen kuutioiden määrä.",
    noScrambles:
      "MBLD sekoituksia ei ole saatavilla. Valitse ensin 3×3×3 Multi-Sokkoratkaisu tapahtuma.",
    visualizationNotFound:
      "Visualisointimodaalia ei löytynyt. Päivitä sivu ja yritä uudelleen.",
    containerNotFound:
      "Visualisointisäiliötä ei löytynyt. Päivitä sivu ja yritä uudelleen.",
    clickToView:
      "Klikkaa nähdäksesi kaikki kuutiovisualisoinnit ja sekoitukset",
    bestScore: "Paras Tulos",
    worstScore: "Huonoin Tulos",
    meanScore: "Keskimääräinen Tulos",
    averageScore: "Keskimääräinen Tulos",
    attempts: "yritystä",
    totalAttempts: "Yrityksiä Yhteensä",
    clickToViewScrambles: "Klikkaa nähdäksesi kaikki sekoitukset",
    clickToViewScramblesCount: "Klikkaa nähdäksesi kaikki {0} sekoitusta",
    setup: "Multi-Sokkoratkaisu Asetukset",
    results: "Multi-Sokkoratkaisu Tulokset",
    generateScrambles: "Luo Sekoituksia",
    saveResult: "Tallenna Tulos",
    cubeNumber: "Kuutio",
    numberOfCubesMinimum: "Kuutioiden määrä (vähintään 2):",
    numberOfCubesSolved: "Ratkaistujen kuutioiden määrä:",
    saveFirst: "Tallenna ensin tuloksesi.",
    visualizationsTitle: "Multi-Sokkoratkaisu Visualisoinnit ({0} kuutiota)",
    timeLimit: "Aikaraja: {0} minuuttia",
    timeLimitExceeded: "Aikaraja ylitetty. Tulos tulee olemaan DNF.",
    negativePoints: "Negatiiviset pisteet. Tulos tulee olemaan DNF.",
  },
  modals: {
    error: "Virhe",
    warning: "Varoitus",
    info: "Tieto",
    confirm: "Vahvista",
    prompt: "Syöttö Vaaditaan",
  },
  stackmat: {
    error: "Stackmat Virhe",
    noMicrophone:
      "Stackmat ajastimen käynnistys epäonnistui: Mikrofonia ei löytynyt. Liitä mikrofoni ja yritä uudelleen.",
    connected: "Yhdistetty",
    disconnected: "Yhteys Katkaistu",
    settingUp: "Asetetaan...",
  },
  sessions: {
    newSessionTitle: "Uusi Istunto",
    editSessionTitle: "Muokkaa Istuntoa",
    sessionName: "Istunnon Nimi:",
    sessionNamePlaceholder: "Minun Istuntoni",
    puzzleType: "Palapelin Tyyppi:",
    create: "Luo",
    save: "Tallenna",
  },
  scramble: {
    loading: "Ladataan sekoitusta...",
  },
  debug: {
    timerState: "Ajastimen Tila: ",
    spaceHeldFor: "Välilyönti Pidettynä: ",
    currentEvent: "Nykyinen Tapahtuma: ",
    scrambleSource: "Sekoituksen Lähde: ",
  },
  fmc: {
    title: "Vähiten Siirtoja Haaste",
    info: "Ratkaise kuutio mahdollisimman vähillä siirroilla. Sinulla on 60 minuuttia löytää ratkaisu.",
    timeRemaining: "Aikaa Jäljellä:",
    scramble: "Sekoitus:",
    solution: "Ratkaisu:",
    moveCount: "Siirrot:",
    moves: "siirtoa",
    submit: "Lähetä",
    resultTitle: "FMC Tulos",
    resultTime: "Aika:",
    resultSolution: "Ratkaisu:",
    resultOk: "OK",
    solutionPlaceholder:
      "Syötä ratkaisusi tähän käyttäen vakio WCA merkintätapaa...",
    notationHelp: "Merkintätavan Ohje:",
    notationHelpContent:
      "Sivujen kääntö: U, D, L, R, F, B (liitteinä ' tai 2)<br>Leveät siirrot: Uw, Dw, jne.<br>Viipalesiirrot: M, E, S<br>Pyöritykset: x, y, z (ei lasketa kokonaissiirtoihin)",
    submitSolution: "Lähetä Ratkaisu",
    validSolution: "Kelvollinen ratkaisu",
    invalidNotation: "Virheellinen merkintätapa havaittu",
    bestMoves: "Parhaat Siirrot",
    worstMoves: "Huonoimmat Siirrot",
    meanMoves: "Keskimääräiset Siirrot",
    bestMo3: "Paras mo3",
    averageMoves: "Keskimääräiset Siirrot",
    attempts: "yritystä",
    totalAttempts: "Yrityksiä Yhteensä",
    tooManyMoves: "Ratkaisu ylittää 80 siirron rajan",
    timeExceeded:
      "Aikaraja ylitetty. Ratkaisusi merkitään DNF:ksi jos sitä ei lähetetä.",
    confirmClose:
      "Oletko varma, että haluat sulkea? Yrityksesi merkitään DNF:ksi.",
    dnfReasonTimeout: "Aikaraja ylitetty",
    dnfReasonInvalid: "Virheellinen merkintätapa",
    dnfReasonTooManyMoves: "Ratkaisu ylittää 80 siirtoa",
    dnfReasonAbandoned: "Yritys hylätty",
    confirmSubmit: "Oletko varma, että haluat lähettää ratkaisusi?",
    pressToStart: "Paina välilyöntiä aloittaaksesi FMC yrityksen",
    solutionAccepted: "Ratkaisu hyväksytty",
    clickToViewTwizzle:
      "Klikkaa alla olevaa linkkiä nähdäksesi ratkaisun Twizzlessa",
    viewOnTwizzle: "Näytä Twizzlessa",
    moveCountLabel: "Siirtojen määrä:",
    movesHTM: "siirtoa (HTM)",
    timeUsedLabel: "Käytetty aika:",
    loadingFMC: "ladataan FMC",
    generatingScramble: "luodaan sekoitusta ja valmistellaan käyttöliittymää",
  },
  tutorial: {
    welcomeTitle: "Tervetuloa scTimeriin!",
    welcomeSubtitle: "Ammattimainen speedcubing ajastimesi",
    selectLanguage: "Valitse Kieli:",
    feature1: "WCA Standardi Ajastin",
    feature2: "Edistyneet Tilastot",
    feature3: "Kaikki WCA Tapahtumat",
    feature4: "Sekoitusgeneraattori",
    welcomeDescription:
      "Haluaisitko nopean kierroksen oppiaksesi käyttämään scTimeriä tehokkaasti? Opastus johdattaa sinut pääominaisuuksien läpi vain muutamassa vaiheessa.",
    skipTutorial: "Ohita Opastus",
    startTour: "Aloita Kierros",
    step1: {
      title: "Sekoituksen Näyttö",
      text: "Tämä näyttää sekoitussekvenssin nykyiselle palapeliisi. Jokainen sekoitus luodaan satunnaisesti WCA standardien mukaisesti.",
    },
    step2: {
      title: "Ajastimen Ohjaimet",
      text: "Pidä VÄLILYÖNTIÄ pohjassa aloittaaksesi ajanotto, päästä irti aloittaaksesi ratkaisun. Mobiililaitteessa napauta ja pidä ajastinaluetta. Ajastin noudattaa WCA tarkastusstandardeja.",
    },
    step3: {
      title: "Tapahtuman Valitsin",
      text: "Valitse kaikista WCA tapahtumista mukaan lukien 3x3x3, 2x2x2, 4x4x4 ja monet muut palapelityypit. Klikkaa tai napauta avataksesi pudotusvalikon.",
    },
    step4: {
      title: "Tilastojen Seuranta",
      text: "Seuraa edistymistäsi yksityiskohtaisilla tilastoilla mukaan lukien paras aika, 5:n, 12:n ja 100:n keskiarvot. Klikkaa mitä tahansa tilastoa nähdäksesi lisää yksityiskohtia.",
    },
    step5: {
      title: "Uuden Sekoituksen Luominen",
      text: "Luo uusi sekoitus kun olet valmis seuraavaan ratkaisuun. Näppäinoikotie: Paina N tai klikkaa sekoituskuvaketta.",
    },
    step6: {
      title: "Asetukset ja Mukauttaminen",
      text: "Mukauta ajastinkokemustasi tarkastusajalla, ääniasetuksilla, ajastintiloilla ja näyttöasetuksilla. Näppäinoikotie: Paina S.",
    },
    step7: {
      title: "Näppäinoikotiet",
      text: "Hallitse nämä oikotiet: VÄLILYÖNTI (käynnistä/pysäytä ajastin), N (uusi sekoitus), S (asetukset), ESC (sulje modaalit), Nuolet (navigoi). Mobiililaitteessa käytä pyyhkäisyeleitä!",
    },
    step8: {
      title: "Mobiilieleet",
      text: "Mobiililaitteissa: Pyyhkäise vasemmalle avataksesi aikapaneelin, pyyhkäise oikealle sulkeaksesi sen, napauta ja pidä ajastinta käynnistääksesi, kaksoisnapauta sekoitusta kopioidaksesi. Nipistä zoomaaksesi visualisointeja.",
    },
    step9: {
      title: "Ammattilaisvinkit ja Ominaisuudet",
      text: "Ota tarkastusaika käyttöön asetuksissa WCA harjoittelua varten. Käytä eri istuntoja seurataksesi eri tapahtumia. Vie aikasi analysoitavaksi. Ajastin toimii offline-tilassa PWA:na!",
    },
    previous: "Edellinen",
    next: "Seuraava",
    finish: "Valmis",
    close: "Sulje",
    stepCounter: "/",
    restartTutorial: "Käynnistä Opastus Uudelleen",
  },
};
