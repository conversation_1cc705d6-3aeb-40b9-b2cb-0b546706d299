// French language file
export default {
  dir: "ltr",
  settings: {
    title: "Paramètres",
    save: "<PERSON>re<PERSON><PERSON><PERSON>",
    close: "<PERSON><PERSON><PERSON>",
    language: "Langue",
  },
  info: {
    title: "Informations",
    pwaInstall: {
      title: "Installer comme App",
      description:
        "Installez scTimer comme une Application Web Progressive pour la meilleure expérience. Fonctionne hors ligne et ressemble à une application native.",
      install: "Installer l'App",
      iosTitle: "Installation iOS/iPad :",
      iosStep1: "1. Appuyez sur le bouton Partager",
      iosStep2:
        "2. Faites défiler vers le bas et appuyez sur \"Ajouter à l'écran d'accueil\"",
      iosStep3: '3. Appuyez sur "Ajouter" pour installer',
      note: "Disponible sur Chrome, Safari et autres navigateurs modernes",
    },
    shortcuts: {
      title: "<PERSON><PERSON><PERSON><PERSON>",
      timer: "Contrôles du Chronomètre",
      spacebar: "Démarrer/arrêter le chronomètre",
      escape: "Annuler l'inspection et fermer les modales",
      navigation: "Navigation et Actions",
      generate: "Générer un nouveau mélange",
      list: "Basculer la liste des temps",
      settings: "Ouvrir les paramètres",
      edit: "Modifier le mélange actuel",
      copy: "Copier le mélange dans le presse-papiers",
      stats: "Ouvrir les statistiques détaillées",
      display: "Basculer l'Affichage",
      visualization: "Basculer la visualisation du puzzle",
      statistics: "Basculer l'affichage des statistiques",
      darkMode: "Basculer le mode sombre",
      inspection: "Basculer l'inspection WCA",
      learning: "Ouvrir la base de données d'algorithmes",
      penalties: "Gestion des Pénalités",
      removePenalty: "Supprimer la pénalité du dernier solve",
      addPlus2: "Ajouter une pénalité +2 au dernier solve",
      addDNF: "Ajouter une pénalité DNF au dernier solve",
      session: "Gestion des Sessions",
      emptySession: "Vider la session actuelle",
      exportSession: "Exporter la session actuelle",
      eventSwitching: "Changement d'Événements",
      alt2to7: "Passer aux cubes 2×2×2 à 7×7×7",
      altP: "Passer au Pyraminx",
      altM: "Passer au Megaminx",
      altC: "Passer au Clock",
      altS: "Passer au Skewb",
      alt1: "Passer au Square-1",
      altF: "Passer au 3×3×3 Moins de Mouvements",
      altO: "Passer au 3×3×3 Une Main",
      blindfolded: "Événements à l'Aveugle",
      altCtrl3: "Passer au 3×3×3 à l'Aveugle",
      altCtrl4: "Passer au 4×4×4 à l'Aveugle",
      altCtrl5: "Passer au 5×5×5 à l'Aveugle",
      altCtrl6: "Passer au 3×3×3 Multi-Aveugle",
      sessionMgmt: "Gestion des Sessions",
      altN: "Créer une nouvelle session",
    },
    gestures: {
      title: "Gestes Mobiles",
      swipeDown: "Glisser vers le Bas",
      swipeDownDesc: "Supprimer le dernier solve",
      swipeUp: "Glisser vers le Haut",
      swipeUpDesc: "Alterner les pénalités (aucune/+2/DNF)",
      swipeLeft: "Glisser vers la Gauche",
      swipeLeftDesc: "LTR: Nouveau mélange | RTL: Liste des temps",
      swipeRight: "Glisser vers la Droite",
      swipeRightDesc: "LTR: Liste des temps | RTL: Nouveau mélange",
      doubleClick: "Double Clic",
      doubleClickDesc: "Copier le mélange actuel (PC/Mobile)",
      longPress: "Appui Long/Clic et Maintenir",
      longPressDesc: "Modifier le mélange actuel (PC/Mobile)",
    },
    learning: {
      title: "Base de Données d'Algorithmes",
      description: "Apprenez et pratiquez les algorithmes de speedcubing",
      puzzle: "Puzzle",
      method: "Méthode",
      step: "Étape",
      case: "Cas",
      algorithm: "Algorithme",
      alternatives: "Algorithmes Alternatifs",
      difficulty: "Difficulté",
      moveCount: "Nombre de Mouvements",
      category: "Catégorie",
      source: "Source",
      noAlgorithms: "Aucun algorithme disponible pour cette sélection",
      loadingError:
        "Erreur lors du chargement de la base de données d'algorithmes",
    },
    features: {
      title: "Fonctionnalités Principales",
      timer: "Chronomètre Professionnel",
      timerDesc: "Chronométrage conforme WCA avec mode d'inspection",
      puzzles: "Tous les Événements WCA",
      puzzlesDesc: "Support complet pour tous les événements officiels WCA",
      statistics: "Statistiques Avancées",
      statisticsDesc: "Analyses détaillées avec ao5, ao12, ao100",
      scrambles: "Mélanges Officiels",
      scramblesDesc:
        "Génération de mélanges standard WCA avec visualisation 2D",
      multilingual: "Support Multilingue",
      multilingualDesc: "15+ langues avec support RTL",
      sync: "Synchronisation Google Drive",
      syncDesc: "Synchronisation inter-appareils avec fusion intelligente",
    },
    sync: {
      title: "Synchronisation Google Drive",
      description:
        "Synchronisez vos temps de solve sur tous les appareils en utilisant Google Drive. Vos données sont stockées en sécurité dans votre compte Google Drive personnel.",
      secure: "Sécurisé et Privé",
      automatic: "Synchronisation Automatique",
      offline: "Support Hors Ligne",
      smartMerge: "Fusion Intelligente",
      note: "Activez la synchronisation Google Drive dans les Paramètres pour garder vos temps synchronisés sur tous vos appareils.",
      status: "Statut :",
      notConnected: "Non connecté",
      connected: "Connecté",
      connect: "Connecter",
      disconnect: "Déconnecter",
      upload: "Télécharger vers Drive",
      download: "Télécharger depuis Drive",
      autoSync: "Synchronisation Automatique",
      autoSyncNote:
        "Synchronisez automatiquement vos temps lorsque vous êtes connecté à Internet",
      uploading: "Téléchargement...",
      downloading: "Téléchargement...",
      syncing: "Synchronisation...",
      uploadSuccess: "Téléchargement réussi",
      downloadSuccess: "Téléchargement réussi",
      uploadFailed: "Échec du téléchargement",
      downloadFailed: "Échec du téléchargement",
      uploadConfirm:
        "Télécharger et fusionner vos temps locaux vers Google Drive ?",
      downloadConfirm:
        "Télécharger et fusionner les données de Google Drive avec vos temps locaux ?",
      downloadMergeConfirm:
        "Ceci fusionnera les données Google Drive avec vos temps locaux. Continuer ?",
      reloadConfirm: "Recharger la page pour voir les changements ?",
      autoSyncEnabled: "Synchronisation automatique activée",
      signInFailed: "Échec de la connexion",
      noSyncFile: "Aucun fichier de synchronisation trouvé",
      noDataFound: "Aucune donnée trouvée",
      uploadCancelled: "Téléchargement annulé",
      downloadCancelled: "Téléchargement annulé",
      syncSuccessful: "Synchronisation réussie",
      syncFailed: "Échec de la synchronisation",
      error: "Erreur",
    },
  },
  timerOptions: {
    title: "Options du Chronomètre",
    warningSounds: "Activer les Sons d'Avertissement",
    useInspection: "Utiliser l'Inspection WCA (15s)",
    inspectionSound: "Son d'Inspection :",
    inspectionSoundNone: "Aucun",
    inspectionSoundVoice: "Voix",
    inspectionSoundBeep: "Bip",
    stackmatResetInspection: "Reset Stackmat Déclenche l'Inspection",
    stackmatResetNote:
      "Note : Fonctionne seulement quand le chrono n'est pas à 0.000",
    inputTimer: "Mode Chronomètre de Saisie (Entrer les temps manuellement)",
    timerMode: "Mode Chronomètre :",
    timerModeTimer: "Chronomètre",
    timerModeTyping: "Saisie",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "Bluetooth (Bientôt)",
    microphoneInput: "Entrée Microphone",
    microphoneAuto: "Détection automatique",
    microphoneNote: "Choisissez votre répartiteur Y ou microphone externe",
    decimalPlaces: "Décimales :",
    decimalPlacesNone: "Aucune (12)",
    decimalPlaces1: "1 (12.3)",
    decimalPlaces2: "2 (12.34)",
    decimalPlaces3: "3 (12.345)",
  },
  displayOptions: {
    title: "Options d'Affichage",
    showVisualization: "Afficher la Visualisation du Puzzle",
    showStats: "Afficher les Statistiques",
    showDebug: "Afficher les Informations de Débogage",
    darkMode: "Mode Sombre",
    showFMCKeyboard: "Afficher le Clavier FMC",
    scrambleFontSize: "Taille de Police du Mélange",
  },
  app: {
    title: "scTimer",
    description:
      "Un chronomètre de speedcubing avec inspection WCA et statistiques",
    enterTime: "Entrer le temps",
    enterSolveTime: "Entrer le temps de solve manuellement",
    generateScrambles: "Générer les Mélanges",
    outOf: "Sur :",
    numberOfCubes: "Nombre de cubes (minimum 2) :",
    numberOfCubesSolved: "Nombre de cubes résolus :",
  },
  timer: {
    ready: "Prêt",
    running: "En cours",
    idle: "Inactif",
    inspection: "Inspection",
    holding: "Maintenu",
  },
  stats: {
    title: "Statistiques",
    best: "Meilleur",
    worst: "Pire",
    mean: "Moyenne",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "Meilleur mo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "Solves",
    attempts: "Tentatives",
    moreStats: "Plus de Stats",
  },
  statsDetails: {
    title: "Détails des Statistiques",
    titleFor: "Détails des Statistiques pour",
    overview: "Aperçu",
    averages: "Moyennes",
    records: "Records",
    timeDistribution: "Distribution des Temps",
    progressChart: "Progression dans le Temps",
    sessionAnalysis: "Analyse de Session",
    predictions: "Prédictions",
    standardDeviation: "Écart Type",
    bestSingle: "Meilleur Single",
    bestAo5: "Meilleur ao5",
    bestAo12: "Meilleur ao12",
    bestAo100: "Meilleur ao100",
    bestAo1000: "Meilleur ao1000",
    totalTime: "Temps Total",
    averageTime: "Temps Moyen",
    solvesPerHour: "Solves/Heure",
    consistency: "Consistance",
    nextAo5: "Objectif Prochain ao5",
    nextAo12: "Objectif Prochain ao12",
    improvementRate: "Taux d'Amélioration",
    targetTime: "Temps Cible",
    currentSession: "Session Actuelle",
    allSessions: "Toutes les Sessions",
    importTimes: "Importer les Temps",
    exportJSON: "Exporter JSON",
    exportCSV: "Exporter CSV",
  },
  solveDetails: {
    title: "Détails du Solve",
    time: "Temps",
    date: "Date",
    scramble: "Mélange",
    editedScramble: "Mélange Modifié",
    copyScramble: "Copier le mélange",
    penalty: "Pénalité",
    none: "Aucune",
    comment: "Commentaire",
    addComment: "Ajouter un commentaire...",
    save: "Enregistrer",
    share: "Partager",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "Mélange copié",
    noSolvesToDelete: "Aucun solve à supprimer",
    solveDeleted: "Solve supprimé",
    cannotAddPenaltyMBLD: "Impossible d'ajouter une pénalité au solve MBLD",
    dnfRemoved: "DNF supprimé",
    dnfAdded: "DNF ajouté",
    plus2Added: "Pénalité +2 ajoutée",
    penaltyRemoved: "Pénalité supprimée",
    newScrambleGenerated: "Nouveau mélange généré",
    timesPanelOpened: "Panneau des temps ouvert",
  },
  times: {
    title: "Temps de Solve",
    clear: "Effacer les Temps",
    close: "Fermer",
    delete: "Supprimer le temps",
    confirmClear:
      "Êtes-vous sûr de vouloir effacer tous les temps pour cet événement ?",
    confirmDelete: "Êtes-vous sûr de vouloir supprimer ce temps ?",
  },
  buttons: {
    viewTimes: "Voir les Temps",
    ok: "OK",
    cancel: "Annuler",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3 à l'Aveugle",
    "333fm": "3×3×3 Moins de Mouvements",
    "333oh": "3×3×3 Une Main",
    clock: "Clock",
    minx: "Megaminx",
    pyram: "Pyraminx",
    skewb: "Skewb",
    sq1: "Square-1",
    "444bf": "4×4×4 à l'Aveugle",
    "555bf": "5×5×5 à l'Aveugle",
    "333mbf": "3×3×3 Multi-Aveugle",
  },
  mbld: {
    cubeCount: "Cubes",
    solvedCount: "Cubes Résolus",
    totalCount: "Cubes Totaux",
    totalCubes: "Cubes Totaux",
    cubesSolved: "Cubes Résolus",
    bestPoints: "Meilleurs Points",
    successRate: "Taux de Réussite",
    points: "points",
    save: "Enregistrer le Résultat",
    visualizations: "Visualisations Multi-Aveugle",
    scrambles: "Mélanges Multi-Aveugle",
    enterValidNumber: "Veuillez entrer un nombre valide de cubes résolus.",
    noScrambles:
      "Aucun mélange MBLD disponible. Veuillez d'abord sélectionner l'événement 3×3×3 Multi-Aveugle.",
    visualizationNotFound:
      "Modal de visualisation non trouvé. Veuillez actualiser la page et réessayer.",
    containerNotFound:
      "Conteneur de visualisation non trouvé. Veuillez actualiser la page et réessayer.",
    clickToView:
      "Cliquez pour voir toutes les visualisations et mélanges de cubes",
    bestScore: "Meilleur Score",
    worstScore: "Pire Score",
    meanScore: "Score Moyen",
    averageScore: "Score Moyen",
    attempts: "tentatives",
    totalAttempts: "Tentatives Totales",
    clickToViewScrambles: "Cliquez pour voir tous les mélanges",
    clickToViewScramblesCount: "Cliquez pour voir tous les {0} mélanges",
    setup: "Configuration Multi-Aveugle",
    results: "Résultats Multi-Aveugle",
    generateScrambles: "Générer les Mélanges",
    saveResult: "Enregistrer le Résultat",
    cubeNumber: "Cube",
    numberOfCubesMinimum: "Nombre de cubes (minimum 2) :",
    numberOfCubesSolved: "Nombre de cubes résolus :",
    saveFirst: "Veuillez d'abord enregistrer votre résultat.",
    visualizationsTitle: "Visualisations Multi-Aveugle ({0} cubes)",
    timeLimit: "Limite de temps : {0} minutes",
    timeLimitExceeded: "Limite de temps dépassée. Le résultat sera DNF.",
    negativePoints: "Points négatifs. Le résultat sera DNF.",
  },
  modals: {
    error: "Erreur",
    warning: "Avertissement",
    info: "Information",
    confirm: "Confirmer",
    prompt: "Saisie Requise",
  },
  stackmat: {
    error: "Erreur Stackmat",
    noMicrophone:
      "Échec du démarrage du chronomètre Stackmat : Aucun microphone trouvé. Veuillez connecter un microphone et réessayer.",
    connected: "Connecté",
    disconnected: "Déconnecté",
    settingUp: "Configuration...",
  },
  sessions: {
    newSessionTitle: "Nouvelle Session",
    editSessionTitle: "Modifier la Session",
    sessionName: "Nom de Session :",
    sessionNamePlaceholder: "Ma Session",
    puzzleType: "Type de Puzzle :",
    create: "Créer",
    save: "Enregistrer",
  },
  scramble: {
    loading: "Chargement du mélange...",
  },
  debug: {
    timerState: "État du Chronomètre : ",
    spaceHeldFor: "Espace Maintenu Pendant : ",
    currentEvent: "Événement Actuel : ",
    scrambleSource: "Source du Mélange : ",
  },
  fmc: {
    title: "Défi Moins de Mouvements",
    info: "Résolvez le cube avec le moins de mouvements possible. Vous avez 60 minutes pour trouver une solution.",
    timeRemaining: "Temps :",
    scramble: "Mélange :",
    solution: "Solution :",
    moveCount: "Mouvements :",
    moves: "mouvements",
    submit: "Soumettre",
    resultTitle: "Résultat FMC",
    resultTime: "Temps :",
    resultSolution: "Solution :",
    resultOk: "OK",
    solutionPlaceholder:
      "Entrez votre solution ici en utilisant la notation standard WCA...",
    notationHelp: "Aide Notation :",
    notationHelpContent:
      "Tours de face : U, D, L, R, F, B (avec suffixes ' ou 2)<br>Mouvements larges : Uw, Dw, etc.<br>Mouvements de tranche : M, E, S<br>Rotations : x, y, z (non comptés dans le total)",
    submitSolution: "Soumettre la Solution",
    validSolution: "Solution valide",
    invalidNotation: "Notation invalide détectée",
    bestMoves: "Meilleurs Mouvements",
    worstMoves: "Pires Mouvements",
    meanMoves: "Mouvements Moyens",
    bestMo3: "Meilleur mo3",
    averageMoves: "Mouvements Moyens",
    attempts: "tentatives",
    totalAttempts: "Tentatives Totales",
    tooManyMoves: "La solution dépasse la limite de 80 mouvements",
    timeExceeded:
      "Limite de temps dépassée. Votre solution sera marquée comme DNF si non soumise.",
    confirmClose:
      "Êtes-vous sûr de vouloir fermer ? Votre tentative sera marquée comme DNF.",
    dnfReasonTimeout: "Limite de temps dépassée",
    dnfReasonInvalid: "Notation invalide",
    dnfReasonTooManyMoves: "La solution dépasse 80 mouvements",
    dnfReasonAbandoned: "Tentative abandonnée",
    confirmSubmit: "Êtes-vous sûr de vouloir soumettre votre solution ?",
    pressToStart: "Appuyez sur espace pour commencer la tentative FMC",
    solutionAccepted: "Solution acceptée",
    clickToViewTwizzle:
      "Cliquez sur le lien ci-dessous pour voir la solution dans Twizzle",
    viewOnTwizzle: "Voir sur Twizzle",
    moveCountLabel: "Nombre de mouvements :",
    movesHTM: "mouvements (HTM)",
    timeUsedLabel: "Temps utilisé :",
    loadingFMC: "chargement FMC",
    generatingScramble: "génération du mélange et préparation de l'interface",
  },
  tutorial: {
    welcomeTitle: "Bienvenue sur scTimer !",
    welcomeSubtitle: "Votre chronomètre professionnel de speedcubing",
    selectLanguage: "Sélectionner la Langue :",
    feature1: "Chronomètre Standard WCA",
    feature2: "Statistiques Avancées",
    feature3: "Tous les Événements WCA",
    feature4: "Générateur de Mélanges",
    welcomeDescription:
      "Souhaitez-vous un tour rapide pour apprendre à utiliser scTimer efficacement ? Le tutoriel vous guidera à travers les fonctionnalités principales en quelques étapes seulement.",
    skipTutorial: "Passer le Tutoriel",
    startTour: "Commencer le Tour",
    step1: {
      title: "Affichage du Mélange",
      text: "Ceci montre la séquence de mélange pour votre puzzle actuel. Chaque mélange est généré aléatoirement selon les standards WCA.",
    },
    step2: {
      title: "Contrôles du Chronomètre",
      text: "Appuyez et maintenez la BARRE D'ESPACE pour commencer le chronométrage, relâchez pour commencer à résoudre. Sur mobile, appuyez et maintenez la zone du chronomètre. Le chronomètre suit les standards d'inspection WCA.",
    },
    step3: {
      title: "Sélecteur d'Événements",
      text: "Choisissez parmi tous les événements WCA incluant 3x3x3, 2x2x2, 4x4x4, et bien d'autres types de puzzles. Cliquez ou appuyez pour ouvrir le menu déroulant.",
    },
    step4: {
      title: "Suivi des Statistiques",
      text: "Suivez votre progression avec des statistiques détaillées incluant le meilleur temps, les moyennes de 5, 12, et 100 solves. Cliquez sur n'importe quelle statistique pour voir plus de détails.",
    },
    step5: {
      title: "Générer un Nouveau Mélange",
      text: "Générez un nouveau mélange quand vous êtes prêt pour votre prochain solve. Raccourci clavier : Appuyez sur N ou cliquez sur l'icône de mélange.",
    },
    step6: {
      title: "Paramètres et Personnalisation",
      text: "Personnalisez votre expérience de chronomètre avec le temps d'inspection, les options sonores, les modes de chronomètre, et les préférences d'affichage. Raccourci clavier : Appuyez sur S.",
    },
    step7: {
      title: "Raccourcis Clavier",
      text: "Maîtrisez ces raccourcis : BARRE D'ESPACE (démarrer/arrêter chronomètre), N (nouveau mélange), S (paramètres), ESC (fermer modales), Touches fléchées (naviguer). Sur mobile, utilisez les gestes de glissement !",
    },
    step8: {
      title: "Gestes Mobiles",
      text: "Sur appareils mobiles : Glissez à gauche pour ouvrir le panneau des temps, glissez à droite pour le fermer, appuyez et maintenez le chronomètre pour démarrer, double-appui sur le mélange pour le copier. Pincez pour zoomer sur les visualisations.",
    },
    step9: {
      title: "Conseils Pro et Fonctionnalités",
      text: "Activez le temps d'inspection dans les paramètres pour la pratique WCA. Utilisez différentes sessions pour suivre divers événements. Exportez vos temps pour analyse. Le chronomètre fonctionne hors ligne comme PWA !",
    },
    previous: "Précédent",
    next: "Suivant",
    finish: "Terminer",
    close: "Fermer",
    stepCounter: "de",
    restartTutorial: "Redémarrer le Tutoriel",
  },
};
