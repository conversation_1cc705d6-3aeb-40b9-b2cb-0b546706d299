{"3x3x3 Cube": {"name": "3x3x3 Cube", "event_type": "WCA", "methods": {"CFOP": {"name": "CFOP", "description": "Cross, F2L, OLL, PLL - The most popular speedcubing method", "steps": {"OLL": {"name": "Orientation of Last Layer", "description": "Algorithms for orienting all pieces on the last layer", "cases": [{"id": "oll_1", "name": "Dot Case 1", "case_name": "1", "category": "<PERSON>", "algorithm": "(R U2 R') (R' F R F') U2 (R' F R F')", "move_count": 12}, {"id": "oll_2", "name": "Dot Case 2", "case_name": "2", "category": "<PERSON>", "algorithm": "F (R U R' U') F' f (R U R' U') f'", "move_count": 12}, {"id": "oll_3", "name": "Dot Case 3", "case_name": "3", "category": "<PERSON>", "algorithm": "f (R U R' U') f' (U') F (R U R' U') F'", "move_count": 14}, {"id": "oll_4", "name": "Dot Case 4", "case_name": "4", "category": "<PERSON>", "algorithm": "f (R U R' U') f' (U) F (R U R' U') F'", "move_count": 14}, {"id": "oll_17", "name": "Dot Case 17", "case_name": "17", "category": "<PERSON>", "algorithm": "(R U R' U) (R' F R F') U2 (R' F R F')", "move_count": 13}, {"id": "oll_18", "name": "Dot Case 18", "case_name": "18", "category": "<PERSON>", "algorithm": "(R U2 R') (R' F R F') U2 M' (U R U' r')", "move_count": 14}, {"id": "oll_19", "name": "Dot Case 19", "case_name": "19", "category": "<PERSON>", "algorithm": "M U (R U R' U') M' (R' F R F')", "move_count": 11}, {"id": "oll_20", "name": "Dot Case 20", "case_name": "20", "category": "<PERSON>", "algorithm": "(r U R' U') M2 (U R U' R') U' M'", "move_count": 11}, {"id": "oll_5", "name": "Square Shape 5", "case_name": "5", "category": "Square Shape", "algorithm": "r' U2 (R U R' U) r", "move_count": 7}, {"id": "oll_6", "name": "Square Shape 6", "case_name": "6", "category": "Square Shape", "algorithm": "r <PERSON> (R' U' R U') r'", "move_count": 7}, {"id": "oll_7", "name": "Lightning Shape 7", "case_name": "7", "category": "Lightning Shape", "algorithm": "r (U R' U R) U2 r'", "move_count": 7}, {"id": "oll_8", "name": "Lightning Shape 8", "case_name": "8", "category": "Lightning Shape", "algorithm": "r' (U' R U' R') U2 r", "move_count": 8}, {"id": "oll_11", "name": "Lightning Shape 11", "case_name": "11", "category": "Lightning Shape", "algorithm": "M (R U R' U R U2 R') U M'", "move_count": 10}, {"id": "oll_12", "name": "Lightning Shape 12", "case_name": "12", "category": "Lightning Shape", "algorithm": "M' (<PERSON>' <PERSON>' R U' R' U2 R) U' M", "move_count": 11}, {"id": "oll_39", "name": "Lightning Shape 39", "case_name": "39", "category": "Lightning Shape", "algorithm": "L F' (L' U' L U) F U' L'", "move_count": 10}, {"id": "oll_40", "name": "Lightning Shape 40", "case_name": "40", "category": "Lightning Shape", "algorithm": "R' F (R U R' U') F' U R", "move_count": 10}, {"id": "oll_9", "name": "Fish Shape 9", "case_name": "9", "category": "Fish Shape", "algorithm": "(R U R' U') (R' F R) (R U R' U') F'", "move_count": 13}, {"id": "oll_10", "name": "Fish Shape 10", "case_name": "10", "category": "Fish Shape", "algorithm": "(R U R' U) (R' F R F') (R U2 R')", "move_count": 11}, {"id": "oll_35", "name": "Fish Shape 35", "case_name": "35", "category": "Fish Shape", "algorithm": "(R U2 R') (R' F R F') (R U2 R')", "move_count": 10}, {"id": "oll_37", "name": "Fish Shape 37", "case_name": "37", "category": "Fish Shape", "algorithm": "F R (U' R' U') (R U R') F'", "move_count": 9}, {"id": "oll_13", "name": "Knight Move Shape 13", "case_name": "13", "category": "Knight Move Shape", "algorithm": "(r U' r') U' (r U r') (F' U F)", "move_count": 10}, {"id": "oll_14", "name": "Knight Move Shape 14", "case_name": "14", "category": "Knight Move Shape", "algorithm": "R' F (R U R') F' R (F U' F')", "move_count": 10}, {"id": "oll_15", "name": "Knight Move Shape 15", "case_name": "15", "category": "Knight Move Shape", "algorithm": "(r' U' r) (R' U' R U) (r' U r)", "move_count": 10}, {"id": "oll_16", "name": "Knight Move Shape 16", "case_name": "16", "category": "Knight Move Shape", "algorithm": "(r U r') (R U R' U') (r U' r')", "move_count": 10}, {"id": "oll_34", "name": "C Shape 34", "case_name": "34", "category": "C <PERSON>", "algorithm": "(r U r') (R U R' U') (r U' r')", "move_count": 10}, {"id": "oll_46", "name": "C Shape 46", "case_name": "46", "category": "C <PERSON>", "algorithm": "R' U' (R' F R F') U R", "move_count": 8}, {"id": "oll_29", "name": "Awkward <PERSON> 29", "case_name": "29", "category": "<PERSON><PERSON><PERSON><PERSON>", "algorithm": "(R U R') U' (R U' R') (F' U' F) (R U R')", "move_count": 14}, {"id": "oll_30", "name": "Awkward Shape 30", "case_name": "30", "category": "<PERSON><PERSON><PERSON><PERSON>", "algorithm": "F U (R U2 R') U' (R U2 R') U' F'", "move_count": 12}, {"id": "oll_41", "name": "Awkward <PERSON> 41", "case_name": "41", "category": "<PERSON><PERSON><PERSON><PERSON>", "algorithm": "(R U R' U) (R U2 R') F (R U R' U') F'", "move_count": 14}, {"id": "oll_42", "name": "Awkward <PERSON> 42", "case_name": "42", "category": "<PERSON><PERSON><PERSON><PERSON>", "algorithm": "(R' U' R U') (R' U2 R) F (R U R' U') F'", "move_count": 13}, {"id": "oll_28", "name": "All Corners Oriented 28", "case_name": "28", "category": "All Corners Oriented", "algorithm": "(r U R' U') M (U R U' R')", "move_count": 9}, {"id": "oll_57", "name": "All Corners Oriented 57", "case_name": "57", "category": "All Corners Oriented", "algorithm": "(R U R' U') M' (U R U' r')", "move_count": 9}, {"id": "oll_47", "name": "L Shapes 47", "case_name": "47", "category": "<PERSON>", "algorithm": "F' (L' U' L U) (L' U' L U) F", "move_count": 10}, {"id": "oll_48", "name": "L Shapes 48", "case_name": "48", "category": "<PERSON>", "algorithm": "F (R U R' U') (R U R' U') F'", "move_count": 10}, {"id": "oll_49", "name": "L Shapes 49", "case_name": "49", "category": "<PERSON>", "algorithm": "r U' (r2 U) (r2 U) (r2) U' r", "move_count": 10}, {"id": "oll_50", "name": "L Shapes 50", "case_name": "50", "category": "<PERSON>", "algorithm": "r' U (r2 U') (r2 U') (r2) U r'", "move_count": 9}, {"id": "oll_53", "name": "L Shapes 53", "case_name": "53", "category": "<PERSON>", "algorithm": "(r' U' R U') (R' U R U') (R' U2 r)", "move_count": 11}, {"id": "oll_54", "name": "L Shapes 54", "case_name": "54", "category": "<PERSON>", "algorithm": "(r U R' U) (R U' R' U) (R U2 r')", "move_count": 11}, {"id": "oll_51", "name": "Line Shapes 51", "case_name": "51", "category": "Line Shapes", "algorithm": "f (R U R' U') (R U R' U') f'", "move_count": 10}, {"id": "oll_52", "name": "Line Shapes 52", "case_name": "52", "category": "Line Shapes", "algorithm": "R' (F' U' F U') (R U R' U) R", "move_count": 11}, {"id": "oll_55", "name": "Line Shapes 55", "case_name": "55", "category": "Line Shapes", "algorithm": "R U2 R2 (U' R U' R') U2 (F R F')", "move_count": 11}, {"id": "oll_56", "name": "Line Shapes 56", "case_name": "56", "category": "Line Shapes", "algorithm": "(r U r') (U R U' R') (U R U' R') (r U' r')", "move_count": 14}, {"id": "oll_21", "name": "OCLL 21", "case_name": "21", "category": "OCLL", "algorithm": "(R U R' U) (R U' R' U) (R U2 R')", "move_count": 11}, {"id": "oll_22", "name": "OCLL 22", "case_name": "22", "category": "OCLL", "algorithm": "R U2 (R2' U') (R2 U') (R2' U') U' R", "move_count": 10}, {"id": "oll_23", "name": "OCLL 23", "case_name": "23", "category": "OCLL", "algorithm": "R2 D (R' U2 R) D' (R' U2 R')", "move_count": 9}, {"id": "oll_24", "name": "OCLL 24", "case_name": "24", "category": "OCLL", "algorithm": "(r U R' U') (r' F R F')", "move_count": 8}, {"id": "oll_25", "name": "OCLL 25", "case_name": "25", "category": "OCLL", "algorithm": "(F' r U R') (U' r' F R)", "move_count": 9}, {"id": "oll_26", "name": "OCLL 26", "case_name": "26", "category": "OCLL", "algorithm": "R <PERSON> (R' U' R U') R'", "move_count": 8}, {"id": "oll_27", "name": "OCLL 27", "case_name": "27", "category": "OCLL", "algorithm": "(R U R' U) (R U2 R')", "move_count": 7}, {"id": "oll_31", "name": "P Shapes 31", "case_name": "31", "category": "<PERSON>", "algorithm": "(R' U' F) (U R U' R') F' R", "move_count": 9}, {"id": "oll_32", "name": "P Shapes 32", "case_name": "32", "category": "<PERSON>", "algorithm": "S (R U R' U') (R' F R f')", "move_count": 9}, {"id": "oll_43", "name": "<PERSON> 43", "case_name": "43", "category": "<PERSON>", "algorithm": "R' U' (F' U F) R", "move_count": 7}, {"id": "oll_44", "name": "<PERSON> 44", "case_name": "44", "category": "<PERSON>", "algorithm": "f (R U R' U') f'", "move_count": 6}, {"id": "oll_33", "name": "T Shapes 33", "case_name": "33", "category": "<PERSON>", "algorithm": "(R U R' U') (R' F R F')", "move_count": 8}, {"id": "oll_45", "name": "T Shapes 45", "case_name": "45", "category": "<PERSON>", "algorithm": "F (R U R' U') F'", "move_count": 6}, {"id": "oll_36", "name": "<PERSON> <PERSON>hape 36", "case_name": "36", "category": "<PERSON>pe", "algorithm": "(L' U' L U') (L' U L U) (L F' L' F)", "move_count": 13}, {"id": "oll_38", "name": "<PERSON> <PERSON><PERSON><PERSON> 38", "case_name": "38", "category": "<PERSON>pe", "algorithm": "(R U R' U) (R U' R' U') (R' F R F')", "move_count": 12}]}, "PLL": {"name": "Permutation of Last Layer", "description": "Algorithms for permuting all pieces on the last layer", "cases": [{"id": "pll_aa_perm", "name": "Aa Perm", "case_name": "Aa Perm", "category": "Aa Perm", "algorithm": "x (R' U R') D2 (R U' R') D2 R2 x'", "move_count": 11, "piece_movement": "U0U2,U2U8,U8U0"}, {"id": "pll_ab_perm", "name": "<PERSON><PERSON>", "case_name": "<PERSON><PERSON>", "category": "<PERSON><PERSON>", "algorithm": "x R2 D2 (R U R') D2 (R U' R) x'", "move_count": 11, "piece_movement": "U2U0,U8U2,U0U8"}, {"id": "pll_e_perm", "name": "E Perm", "case_name": "E Perm", "category": "E Perm", "algorithm": "x' (R U' R' D) (R U R' D') (R U R' D) (R U' R' D') x", "move_count": 19, "piece_movement": "U0U6,U6U0,U2U8,U8U2"}, {"id": "pll_f_perm", "name": "F Perm", "case_name": "F Perm", "category": "F Perm", "algorithm": "(R' U' F') (R U R' U') R' F R2 (U' R' U') (R U R' U) R", "move_count": 19, "piece_movement": "U1U7,U7U1,U2U8,U8U2"}, {"id": "pll_ga_perm", "name": "Ga Perm", "case_name": "Ga Perm", "category": "Ga Perm", "algorithm": "R2 (U R' U R' U' R U') R2 D (U' R' U R) D'", "move_count": 15, "piece_movement": "U0U2,U2U6,U6U0,U3U5,U5U1,U1U3"}, {"id": "pll_gb_perm", "name": "Gb Perm", "case_name": "Gb Perm", "category": "Gb Perm", "algorithm": "(R' U' R U) D' R2 (U R' U R U' R U') R2 D", "move_count": 15, "piece_movement": "U0U6,U6U8,U8U0,U3U1,U1U7,U7U3"}, {"id": "pll_gc_perm", "name": "Gc Perm", "case_name": "Gc Perm", "category": "Gc Perm", "algorithm": "R2 (U' R U' R U R' U) R2 D' (U R U' R') D", "move_count": 15, "piece_movement": "U0U6,U6U8,U8U0,U3U5,U5U7,U7U3"}, {"id": "pll_gd_perm", "name": "Gd Perm", "case_name": "Gd Perm", "category": "Gd Perm", "algorithm": "(R U R' U') D R2 (U' R U' R' U R' U) R2 D'", "move_count": 15, "piece_movement": "U6U0,U0U2,U2U6,U1U3,U3U7,U7U1"}, {"id": "pll_h_perm", "name": "H Perm", "case_name": "H Perm", "category": "H Perm", "algorithm": "(M2 U' M2) U2 (M2 U' M2)", "move_count": 7, "piece_movement": "U7U1,U1U7,U5U3,U3U5"}, {"id": "pll_ja_perm", "name": "<PERSON><PERSON>", "case_name": "<PERSON><PERSON>", "category": "<PERSON><PERSON>", "algorithm": "(R' U L') U2 (R U' R') U2 R L", "move_count": 11, "piece_movement": "U8U2,U2U8,U5U1,U1U5"}, {"id": "pll_jb_perm", "name": "<PERSON><PERSON>", "case_name": "<PERSON><PERSON>", "category": "<PERSON><PERSON>", "algorithm": "(R U R' F') (R U R' U') R' F R2 U' R'", "move_count": 13, "piece_movement": "U2U8,U8U2,U5U7,U7U5"}, {"id": "pll_na_perm", "name": "Na Perm", "case_name": "Na Perm", "category": "Na Perm", "algorithm": "(R U R' U) (R U R' F') (R U R' U') R' F R2 U' R' U2 (R U' R')", "move_count": 21, "piece_movement": "U6U2,U2U6,U5U3,U3U5"}, {"id": "pll_nb_perm", "name": "Nb Perm", "case_name": "Nb Perm", "category": "Nb Perm", "algorithm": "(R' U R U' R') (F' U' F) (R U R') (F R' F') (R U' R)", "move_count": 17, "piece_movement": "U8U0,U0U8,U5U3,U3U5"}, {"id": "pll_ra_perm", "name": "Ra <PERSON>", "case_name": "Ra <PERSON>", "category": "Ra <PERSON>", "algorithm": "(R U' R' U') (R U R D) (R' U' R D') (R' U2 R')", "move_count": 16, "piece_movement": "U8U2,U2U8,U1U3,U3U1"}, {"id": "pll_rb_perm", "name": "Rb <PERSON>", "case_name": "Rb <PERSON>", "category": "Rb <PERSON>", "algorithm": "(R' <PERSON>) (R U2) (R' F R) (U R' U' R') F' R2", "move_count": 13, "piece_movement": "U8U2,U2U8,U7U3,U3U7"}, {"id": "pll_t_perm", "name": "T Perm", "case_name": "T Perm", "category": "T Perm", "algorithm": "(R U R' U') (R' F R2) (U' R' U') (R U R' F')", "move_count": 14, "piece_movement": "U8U2,U2U8,U5U3,U3U5"}, {"id": "pll_ua_perm", "name": "Ua Perm", "case_name": "Ua Perm", "category": "Ua Perm", "algorithm": "(M2 U M) U2 (M' U M2)", "move_count": 8, "piece_movement": "U5U3,U3U5,U3U7,U7U3,U7U5,U5U7"}, {"id": "pll_ub_perm", "name": "Ub Perm", "case_name": "Ub Perm", "category": "Ub Perm", "algorithm": "(M2 U' M) U2 (M' U' M2)", "move_count": 8, "piece_movement": "U3U5,U5U3,U5U7,U7U5,U7U3,U3U7"}, {"id": "pll_v_perm", "name": "V Perm", "case_name": "V Perm", "category": "V Perm", "algorithm": "(R' U R' U') (R D' R' D) (R' U D') (R2 U' R2) D R2", "move_count": 16, "piece_movement": "U5U1,U1U5,U8U0,U0U8"}, {"id": "pll_y_perm", "name": "Y Perm", "case_name": "Y Perm", "category": "Y Perm", "algorithm": "F R (U' R' U') (R U R' F') (R U R' U') (R' F R F')", "move_count": 17, "piece_movement": "U8U0,U0U8,U3U1,U1U3"}, {"id": "pll_z_perm", "name": "Z Perm", "case_name": "Z Perm", "category": "Z Perm", "algorithm": "(M2 U) (M2 U) (M' U2) M2 (U2 M')", "move_count": 9, "piece_movement": "U5U1,U1U5,U7U3,U3U7"}]}, "F2L": {"name": "First Two Layers", "description": "Algorithms for solving the first two layers simultaneously", "cases": [{"id": "f2l_1", "name": "Case 1", "case_name": "1", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "U R U' R'", "move_count": 4}, "Front-Left": {"algorithm": "F' r U r'", "move_count": 4}, "Back-Left": {"algorithm": "U L U' L'", "move_count": 4}, "Back-Right": {"algorithm": "U f R' f'", "move_count": 4}}}, {"id": "f2l_2", "name": "Case 2", "case_name": "2", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "F R' F' R", "move_count": 4}, "Front-Left": {"algorithm": "U' L' U L", "move_count": 4}, "Back-Left": {"algorithm": "l U L' U' M'", "move_count": 5}, "Back-Right": {"algorithm": "U' R' U R", "move_count": 4}}}, {"id": "f2l_3", "name": "Case 3", "case_name": "3", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "F' U' F", "move_count": 3}, "Front-Left": {"algorithm": "L' U' L", "move_count": 3}, "Back-Left": {"algorithm": "y R' U' R", "move_count": 4}, "Back-Right": {"algorithm": "R' U' R", "move_count": 3}}}, {"id": "f2l_4", "name": "Case 4", "case_name": "4", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "R U R'", "move_count": 3}, "Front-Left": {"algorithm": "F U F'", "move_count": 3}, "Back-Left": {"algorithm": "L U L'", "move_count": 3}, "Back-Right": {"algorithm": "f R f'", "move_count": 3}}}, {"id": "f2l_5", "name": "Case 5", "case_name": "5", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "U' R U R' U2 R U' R'", "move_count": 8}, "Front-Left": {"algorithm": "U R' F r U' r' F' R", "move_count": 8}, "Back-Left": {"algorithm": "U' L U L' U2 L U' L'", "move_count": 8}, "Back-Right": {"algorithm": "U' R' F R U R' U' F' R", "move_count": 9}}}, {"id": "f2l_6", "name": "Case 6", "case_name": "6", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "U' r U' R' U R U r'", "move_count": 8}, "Front-Left": {"algorithm": "U L' U' L U2 L' U L", "move_count": 8}, "Back-Left": {"algorithm": "U r U' r' U' L U F L'", "move_count": 9}, "Back-Right": {"algorithm": "U R' U' R U2 R' U R", "move_count": 8}}}, {"id": "f2l_7", "name": "Case 7", "case_name": "7", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "U' R U2 R' U' R U2 R'", "move_count": 8}, "Front-Left": {"algorithm": "F U R U2 R' U F'", "move_count": 7}, "Back-Left": {"algorithm": "U' L U2 L' U2 L U' L'", "move_count": 8}, "Back-Right": {"algorithm": "r U2 R2 U' R2 U' r'", "move_count": 7}}}, {"id": "f2l_8", "name": "Case 8", "case_name": "8", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "d R' U2 R U R' U2 R", "move_count": 8}, "Front-Left": {"algorithm": "U L' U2 L U L' U2 L", "move_count": 8}, "Back-Left": {"algorithm": "l' U2 L2 U L2 U l", "move_count": 7}, "Back-Right": {"algorithm": "U R' U2 R U R' U2 R", "move_count": 8}}}, {"id": "f2l_9", "name": "Case 9", "case_name": "9", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "U' R U' R' U F' U' F", "move_count": 8}, "Front-Left": {"algorithm": "U L' U' L U' L' U' L", "move_count": 8}, "Back-Left": {"algorithm": "y U R' U' R U' R' U' R", "move_count": 9}, "Back-Right": {"algorithm": "U R' U' R U' R' U' R", "move_count": 8}}}, {"id": "f2l_10", "name": "Case 10", "case_name": "10", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "U' R U R' U R U R'", "move_count": 8}, "Front-Left": {"algorithm": "U L' U L U' F U F'", "move_count": 8}, "Back-Left": {"algorithm": "U' L U L' U L U L'", "move_count": 8}, "Back-Right": {"algorithm": "U R' U R U' f R f'", "move_count": 8}}}, {"id": "f2l_11", "name": "Case 11", "case_name": "11", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "U' R U2 R' U F' U' F", "move_count": 8}, "Front-Left": {"algorithm": "L' U L U' L' U L U2 L' U L", "move_count": 11}, "Back-Left": {"algorithm": "U' L U2 L' U f' L' f", "move_count": 8}, "Back-Right": {"algorithm": "R' U R U' R' U R U2 R' U R", "move_count": 11}}}, {"id": "f2l_12", "name": "Case 12", "case_name": "12", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "R U' R' U R U' R' U2 R U' R'", "move_count": 11}, "Front-Left": {"algorithm": "U L' U2 L U' F U F'", "move_count": 8}, "Back-Left": {"algorithm": "L' U2 L2 U L2 U L", "move_count": 7}, "Back-Right": {"algorithm": "U R' U2 R U' f R f'", "move_count": 8}}}, {"id": "f2l_13", "name": "Case 13", "case_name": "13", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "y' U R' U R U' R' U' R", "move_count": 9}, "Front-Left": {"algorithm": "U L' U L U' L' U' L", "move_count": 8}, "Back-Left": {"algorithm": "d L' U L U' L' U' L", "move_count": 8}, "Back-Right": {"algorithm": "U R' U R U' R' U' R", "move_count": 8}}}, {"id": "f2l_14", "name": "Case 14", "case_name": "14", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "U' R U' R' U R U R'", "move_count": 8}, "Front-Left": {"algorithm": "d' L U' L' U L U L'", "move_count": 8}, "Back-Left": {"algorithm": "U' L U' L' U L U L'", "move_count": 8}, "Back-Right": {"algorithm": "y U' R U' R' U R U R'", "move_count": 9}}}, {"id": "f2l_15", "name": "Case 15", "case_name": "15", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "R' D' R U' R' D R U R U' R'", "move_count": 11}, "Front-Left": {"algorithm": "L' U L U2 F U F'", "move_count": 7}, "Back-Left": {"algorithm": "L U L' U2 L U' L' U L U' L'", "move_count": 11}, "Back-Right": {"algorithm": "R' U R U2 f R f'", "move_count": 7}}}, {"id": "f2l_16", "name": "Case 16", "case_name": "16", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "R U' R' U2 F' U' F", "move_count": 7}, "Front-Left": {"algorithm": "F U' R U' R' U2 F'", "move_count": 7}, "Back-Left": {"algorithm": "L U' L' U2 f' L' f", "move_count": 7}, "Back-Right": {"algorithm": "R' U' R U2 R' U R U' R' U R", "move_count": 11}}}, {"id": "f2l_17", "name": "Case 17", "case_name": "17", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "R U2 R' U' R U R'", "move_count": 7}, "Front-Left": {"algorithm": "y L U2 L' U' L U L'", "move_count": 8}, "Back-Left": {"algorithm": "L U2 L' U' L U L'", "move_count": 7}, "Back-Right": {"algorithm": "y' L U2 L' U' L U L'", "move_count": 8}}}, {"id": "f2l_18", "name": "Case 18", "case_name": "18", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "y' R' U2 R U R' U' R", "move_count": 8}, "Front-Left": {"algorithm": "L' U2 L U L' U' L", "move_count": 7}, "Back-Left": {"algorithm": "y R' U2 R U R' U' R", "move_count": 8}, "Back-Right": {"algorithm": "R' U2 R U R' U' R", "move_count": 7}}}, {"id": "f2l_19", "name": "Case 19", "case_name": "19", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "U R U2 R' U R U' R'", "move_count": 8}, "Front-Left": {"algorithm": "U L' U L2 F' L' F L' U L", "move_count": 10}, "Back-Left": {"algorithm": "U L U2 L' U L U' L'", "move_count": 8}, "Back-Right": {"algorithm": "y U R U2 R' U R U' R'", "move_count": 9}}}, {"id": "f2l_20", "name": "Case 20", "case_name": "20", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "y' U' R' U2 R U' R' U R", "move_count": 9}, "Front-Left": {"algorithm": "U' L' U2 L U' L' U L", "move_count": 8}, "Back-Left": {"algorithm": "y U' R' U2 R U' R' U R", "move_count": 9}, "Back-Right": {"algorithm": "U' R' U2 R U' R' U R", "move_count": 8}}}, {"id": "f2l_21", "name": "Case 21", "case_name": "21", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "U2 R U R' U R U' R'", "move_count": 8}, "Front-Left": {"algorithm": "l' U l U2 l' U' l", "move_count": 7}, "Back-Left": {"algorithm": "L U' L' U2 L U L'", "move_count": 7}, "Back-Right": {"algorithm": "r' U r U2 r' U' r", "move_count": 7}}}, {"id": "f2l_22", "name": "Case 22", "case_name": "22", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "r U' r' U2 r U r'", "move_count": 7}, "Front-Left": {"algorithm": "L' U L U2 L' U' L", "move_count": 7}, "Back-Left": {"algorithm": "l U' l' U2 l U l'", "move_count": 7}, "Back-Right": {"algorithm": "R' U R U2 R' U' R", "move_count": 7}}}, {"id": "f2l_23", "name": "Case 23", "case_name": "23", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "U R U' R' U' R U' R' U R U' R'", "move_count": 12}, "Front-Left": {"algorithm": "F' U' L' U L F L' U L", "move_count": 9}, "Back-Left": {"algorithm": "U L U' L' U' L U' L' U L U' L'", "move_count": 12}, "Back-Right": {"algorithm": "U R' F R' F' R2 U' R' U R", "move_count": 10}}}, {"id": "f2l_24", "name": "Case 24", "case_name": "24", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "F U R U' R' F' R U' R'", "move_count": 9}, "Front-Left": {"algorithm": "U' L' U L U L' U L U' L' U L", "move_count": 12}, "Back-Left": {"algorithm": "U2 r U R' U R U2 B r'", "move_count": 9}, "Back-Right": {"algorithm": "R' U' R U2 R' U' R U R' U' R", "move_count": 11}}}, {"id": "f2l_25", "name": "Case 25", "case_name": "25", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "U' R' F R F' R U R'", "move_count": 8}, "Front-Left": {"algorithm": "U' L' U L F' r U r'", "move_count": 8}, "Back-Left": {"algorithm": "L U' L' U' L U' L' U L U L'", "move_count": 11}, "Back-Right": {"algorithm": "U' R' U M U' R U M'", "move_count": 8}}}, {"id": "f2l_26", "name": "Case 26", "case_name": "26", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "U R U' R' F R' F' R", "move_count": 8}, "Front-Left": {"algorithm": "r U r' U' r' F r F'", "move_count": 8}, "Back-Left": {"algorithm": "L S L' U L S' L'", "move_count": 7}, "Back-Right": {"algorithm": "U f R f' U' R' U' R", "move_count": 8}}}, {"id": "f2l_27", "name": "Case 27", "case_name": "27", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "R U' R' U R U' R'", "move_count": 7}, "Front-Left": {"algorithm": "L' U' L U F' r U r'", "move_count": 8}, "Back-Left": {"algorithm": "L U' L' U L U' L'", "move_count": 7}, "Back-Right": {"algorithm": "R' U2 R' F R F' R", "move_count": 7}}}, {"id": "f2l_28", "name": "Case 28", "case_name": "28", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "R U R' U' F R' F' R", "move_count": 8}, "Front-Left": {"algorithm": "L' U L U' L' U L", "move_count": 7}, "Back-Left": {"algorithm": "L U2 L F' L' F L'", "move_count": 7}, "Back-Right": {"algorithm": "R' U R U' R' U R", "move_count": 7}}}, {"id": "f2l_29", "name": "Case 29", "case_name": "29", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "R' F R F' U R U' R'", "move_count": 8}, "Front-Left": {"algorithm": "L' U' L U L' U' L", "move_count": 7}, "Back-Left": {"algorithm": "y R' U' R U R' U' R", "move_count": 8}, "Back-Right": {"algorithm": "R' U' R U R' U' R", "move_count": 7}}}, {"id": "f2l_30", "name": "Case 30", "case_name": "30", "category": "Basic Insertion", "slots": {"Front-Right": {"algorithm": "R U R' U' R U R'", "move_count": 7}, "Front-Left": {"algorithm": "L F' L' F U' L' U L", "move_count": 8}, "Back-Left": {"algorithm": "L U L' U' L U L'", "move_count": 7}, "Back-Right": {"algorithm": "y' L U L' U' L U L'", "move_count": 8}}}, {"id": "f2l_31", "name": "Case 31", "case_name": "31", "category": "Advanced Insertion", "slots": {"Front-Right": {"algorithm": "U' R' F R F' R U' R'", "move_count": 8}, "Front-Left": {"algorithm": "U L F' L' F L' U L", "move_count": 8}, "Back-Left": {"algorithm": "L U' L F' L' F L'", "move_count": 7}, "Back-Right": {"algorithm": "R' U R' F R F' R", "move_count": 7}}}, {"id": "f2l_32", "name": "Case 32", "case_name": "32", "category": "Advanced Insertion", "slots": {"Front-Right": {"algorithm": "U R U' R' U R U' R' U R U' R'", "move_count": 12}, "Front-Left": {"algorithm": "U' L' U L U' L' U L U' L' U L", "move_count": 12}, "Back-Left": {"algorithm": "L U L' U' L U L' U' L U L'", "move_count": 11}, "Back-Right": {"algorithm": "U' R' U R U' R' U R U' R' U R", "move_count": 12}}}, {"id": "f2l_33", "name": "Case 33", "case_name": "33", "category": "Advanced Insertion", "slots": {"Front-Right": {"algorithm": "U' R U' R' U2 R U' R'", "move_count": 8}, "Front-Left": {"algorithm": "R' D R U' R' D' R", "move_count": 7}, "Back-Left": {"algorithm": "U' L U' L' U2 L U' L'", "move_count": 8}, "Back-Right": {"algorithm": "U' R D R' U R D' R'", "move_count": 8}}}, {"id": "f2l_34", "name": "Case 34", "case_name": "34", "category": "Advanced Insertion", "slots": {"Front-Right": {"algorithm": "U R U R' U2 R U R'", "move_count": 8}, "Front-Left": {"algorithm": "U L' U L U2 L' U L", "move_count": 8}, "Back-Left": {"algorithm": "U L U L' U2 L U L'", "move_count": 8}, "Back-Right": {"algorithm": "U R' U R U R' U2 R", "move_count": 8}}}, {"id": "f2l_35", "name": "Case 35", "case_name": "35", "category": "Advanced Insertion", "slots": {"Front-Right": {"algorithm": "U' R U R' U F' U' F", "move_count": 8}, "Front-Left": {"algorithm": "U2 F U F' U' L' U L", "move_count": 8}, "Back-Left": {"algorithm": "U' L U L' U f' L' f", "move_count": 8}, "Back-Right": {"algorithm": "U' f R f' U R' U' R", "move_count": 8}}}, {"id": "f2l_36", "name": "Case 36", "case_name": "36", "category": "Advanced Insertion", "slots": {"Front-Right": {"algorithm": "U F' U' F U' R U R'", "move_count": 8}, "Front-Left": {"algorithm": "U L' U' L d' L U L'", "move_count": 8}, "Back-Left": {"algorithm": "U f' L' f U' L U L'", "move_count": 8}, "Back-Right": {"algorithm": "U R' U' R U' f R f'", "move_count": 8}}}, {"id": "f2l_37", "name": "Case 37", "case_name": "37", "category": "Advanced Insertion", "slots": {"Front-Right": {"algorithm": "R2 U2 F R2 F' U2 R' U R'", "move_count": 9}, "Front-Left": {"algorithm": "L2 U2 F' L2 F U2 L U' L", "move_count": 9}, "Back-Left": {"algorithm": "L U' L' l' U2 L2 U L2 U l", "move_count": 10}, "Back-Right": {"algorithm": "R' U R r U2 R2 U' R2 U' r'", "move_count": 10}}}, {"id": "f2l_38", "name": "Case 38", "case_name": "38", "category": "Advanced Insertion", "slots": {"Front-Right": {"algorithm": "R U' R' U' R U R' U2 R U' R'", "move_count": 11}, "Front-Left": {"algorithm": "L' U L U' L' U2 L U' L' U L", "move_count": 11}, "Back-Left": {"algorithm": "L U L' U' L U2 L' U' L U L'", "move_count": 11}, "Back-Right": {"algorithm": "R' U' R U2 R' U R U' R' U' R", "move_count": 11}}}, {"id": "f2l_39", "name": "Case 39", "case_name": "39", "category": "Advanced Insertion", "slots": {"Front-Right": {"algorithm": "R U' R' U R U2 R' U R U' R'", "move_count": 11}, "Front-Left": {"algorithm": "L' U' L U L' U2 L U L' U' L", "move_count": 11}, "Back-Left": {"algorithm": "L U L' U2 L U' L' U L U L'", "move_count": 11}, "Back-Right": {"algorithm": "R' U' R U R' U2 R U R' U' R", "move_count": 11}}}, {"id": "f2l_40", "name": "Case 40", "case_name": "40", "category": "Advanced Insertion", "slots": {"Front-Right": {"algorithm": "r U' r' U2 r U r' R U R'", "move_count": 10}, "Front-Left": {"algorithm": "L' U L F R U2 R' F'", "move_count": 8}, "Back-Left": {"algorithm": "l U' l' U2 l U l' L U L'", "move_count": 10}, "Back-Right": {"algorithm": "R' U R r' U r U2 r' U' r", "move_count": 10}}}, {"id": "f2l_41", "name": "Case 41", "case_name": "41", "category": "Advanced Insertion", "slots": {"Front-Right": {"algorithm": "R U' R' r U' r' U2 r U r'", "move_count": 10}, "Front-Left": {"algorithm": "l' U l U2 l' U' l L' U' L", "move_count": 10}, "Back-Left": {"algorithm": "f' L f U' L U L' U L U L'", "move_count": 11}, "Back-Right": {"algorithm": "r' U r U2 r' U' r R' U' R", "move_count": 10}}}]}}}}}}