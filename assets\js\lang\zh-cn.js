// Chinese Simplified language file
export default {
  dir: "ltr",
  settings: {
    title: "设置",
    save: "保存",
    close: "关闭",
    language: "语言",
  },
  info: {
    title: "信息",
    pwaInstall: {
      title: "安装为应用",
      description:
        "将scTimer安装为渐进式Web应用以获得最佳体验。可离线工作，感觉像原生应用。",
      install: "安装应用",
      iosTitle: "iOS/iPad 安装：",
      iosStep1: "1. 点击分享按钮",
      iosStep2: '2. 向下滚动并点击"添加到主屏幕"',
      iosStep3: '3. 点击"添加"进行安装',
      note: "适用于Chrome、Safari和其他现代浏览器",
    },
    shortcuts: {
      title: "键盘快捷键",
      timer: "计时器控制",
      spacebar: "开始/停止计时器",
      escape: "取消检查并关闭模态框",
      navigation: "导航和操作",
      generate: "生成新打乱",
      list: "切换时间列表",
      settings: "打开设置",
      edit: "编辑当前打乱",
      copy: "复制打乱到剪贴板",
      stats: "打开详细统计",
      display: "切换显示",
      visualization: "切换魔方可视化",
      statistics: "切换统计显示",
      darkMode: "切换暗黑模式",
      inspection: "切换WCA检查",
      learning: "打开算法数据库",
      penalties: "罚时管理",
      removePenalty: "移除最近还原的罚时",
      addPlus2: "为最近还原添加+2罚时",
      addDNF: "为最近还原添加DNF罚时",
      session: "会话管理",
      emptySession: "清空当前会话",
      exportSession: "导出当前会话",
      eventSwitching: "项目切换",
      alt2to7: "切换到2×2×2至7×7×7魔方",
      altP: "切换到金字塔",
      altM: "切换到五魔方",
      altC: "切换到魔表",
      altS: "切换到斜转",
      alt1: "切换到SQ1",
      altF: "切换到3×3×3最少步",
      altO: "切换到3×3×3单手",
      blindfolded: "盲拧项目",
      altCtrl3: "切换到3×3×3盲拧",
      altCtrl4: "切换到4×4×4盲拧",
      altCtrl5: "切换到5×5×5盲拧",
      altCtrl6: "切换到3×3×3多盲",
      sessionMgmt: "会话管理",
      altN: "创建新会话",
    },
    gestures: {
      title: "移动端手势",
      swipeDown: "向下滑动",
      swipeDownDesc: "删除最近还原",
      swipeUp: "向上滑动",
      swipeUpDesc: "循环罚时（无/+2/DNF）",
      swipeLeft: "向左滑动",
      swipeLeftDesc: "LTR：新打乱 | RTL：时间列表",
      swipeRight: "向右滑动",
      swipeRightDesc: "LTR：时间列表 | RTL：新打乱",
      doubleClick: "双击",
      doubleClickDesc: "复制当前打乱（PC/移动端）",
      longPress: "长按/点击并保持",
      longPressDesc: "编辑当前打乱（PC/移动端）",
    },
    learning: {
      title: "算法数据库",
      description: "学习和练习速拧算法",
      puzzle: "魔方",
      method: "方法",
      step: "步骤",
      case: "情况",
      algorithm: "算法",
      alternatives: "替代算法",
      difficulty: "难度",
      moveCount: "步数",
      category: "类别",
      source: "来源",
      noAlgorithms: "此选择没有可用的算法",
      loadingError: "加载算法数据库时出错",
    },
    features: {
      title: "主要功能",
      timer: "专业计时器",
      timerDesc: "符合WCA标准的计时，带检查模式",
      puzzles: "所有WCA项目",
      puzzlesDesc: "完全支持所有官方WCA魔方项目",
      statistics: "高级统计",
      statisticsDesc: "详细分析，包含ao5、ao12、ao100",
      scrambles: "官方打乱",
      scramblesDesc: "WCA标准打乱生成，带2D可视化",
      multilingual: "多语言支持",
      multilingualDesc: "15+种语言，支持RTL",
      sync: "Google Drive同步",
      syncDesc: "跨设备同步，智能合并",
    },
    sync: {
      title: "Google Drive同步",
      description:
        "使用Google Drive在所有设备上同步您的还原时间。您的数据安全存储在您的个人Google Drive账户中。",
      secure: "安全私密",
      automatic: "自动同步",
      offline: "离线支持",
      smartMerge: "智能合并",
      note: "在设置中启用Google Drive同步，以保持您的时间在所有设备上同步。",
      status: "状态：",
      notConnected: "未连接",
      connected: "已连接",
      connect: "连接",
      disconnect: "断开连接",
      upload: "上传到Drive",
      download: "从Drive下载",
      autoSync: "自动同步",
      autoSyncNote: "连接到互联网时自动同步您的时间",
      uploading: "上传中...",
      downloading: "下载中...",
      syncing: "同步中...",
      uploadSuccess: "上传成功",
      downloadSuccess: "下载成功",
      uploadFailed: "上传失败",
      downloadFailed: "下载失败",
      uploadConfirm: "上传并合并您的本地时间到Google Drive？",
      downloadConfirm: "从Google Drive下载并合并数据到您的本地时间？",
      downloadMergeConfirm: "这将把Google Drive数据与您的本地时间合并。继续？",
      reloadConfirm: "重新加载页面以查看更改？",
      autoSyncEnabled: "自动同步已启用",
      signInFailed: "登录失败",
      noSyncFile: "未找到同步文件",
      noDataFound: "未找到数据",
      uploadCancelled: "上传已取消",
      downloadCancelled: "下载已取消",
      syncSuccessful: "同步成功",
      syncFailed: "同步失败",
      error: "错误",
    },
  },
  timerOptions: {
    title: "计时器选项",
    warningSounds: "启用警告声音",
    useInspection: "使用WCA检查（15秒）",
    inspectionSound: "检查声音：",
    inspectionSoundNone: "无",
    inspectionSoundVoice: "语音",
    inspectionSoundBeep: "蜂鸣",
    stackmatResetInspection: "Stackmat重置触发检查",
    stackmatResetNote: "注意：仅在计时器不为0.000时工作",
    inputTimer: "输入计时器模式（手动输入时间）",
    timerMode: "计时器模式：",
    timerModeTimer: "计时器",
    timerModeTyping: "输入",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "蓝牙（即将推出）",
    microphoneInput: "麦克风输入",
    microphoneAuto: "自动检测",
    microphoneNote: "选择您的Y分配器或外部麦克风",
    decimalPlaces: "小数位数：",
    decimalPlacesNone: "无（12）",
    decimalPlaces1: "1位（12.3）",
    decimalPlaces2: "2位（12.34）",
    decimalPlaces3: "3位（12.345）",
  },
  displayOptions: {
    title: "显示选项",
    showVisualization: "显示魔方可视化",
    showStats: "显示统计",
    showDebug: "显示调试信息",
    darkMode: "暗黑模式",
    showFMCKeyboard: "显示FMC键盘",
    scrambleFontSize: "打乱字体大小",
  },
  app: {
    title: "scTimer",
    description: "带WCA检查和统计的速拧计时器",
    enterTime: "输入时间",
    enterSolveTime: "手动输入还原时间",
    generateScrambles: "生成打乱",
    outOf: "总共：",
    numberOfCubes: "魔方数量（最少2个）：",
    numberOfCubesSolved: "已还原魔方数量：",
  },
  timer: {
    ready: "就绪",
    running: "运行中",
    idle: "空闲",
    inspection: "检查",
    holding: "保持",
  },
  stats: {
    title: "统计",
    best: "最佳",
    worst: "最差",
    mean: "平均",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "最佳mo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "还原",
    attempts: "尝试",
    moreStats: "更多统计",
  },
  statsDetails: {
    title: "统计详情",
    titleFor: "统计详情",
    overview: "概览",
    averages: "平均值",
    records: "记录",
    timeDistribution: "时间分布",
    progressChart: "进度图表",
    sessionAnalysis: "会话分析",
    predictions: "预测",
    standardDeviation: "标准差",
    bestSingle: "最佳单次",
    bestAo5: "最佳ao5",
    bestAo12: "最佳ao12",
    bestAo100: "最佳ao100",
    bestAo1000: "最佳ao1000",
    totalTime: "总时间",
    averageTime: "平均时间",
    solvesPerHour: "每小时还原数",
    consistency: "一致性",
    nextAo5: "下一个ao5目标",
    nextAo12: "下一个ao12目标",
    improvementRate: "改进率",
    targetTime: "目标时间",
    currentSession: "当前会话",
    allSessions: "所有会话",
    importTimes: "导入时间",
    exportJSON: "导出JSON",
    exportCSV: "导出CSV",
  },
  solveDetails: {
    title: "还原详情",
    time: "时间",
    date: "日期",
    scramble: "打乱",
    editedScramble: "已编辑打乱",
    copyScramble: "复制打乱",
    penalty: "罚时",
    none: "无",
    comment: "评论",
    addComment: "添加评论...",
    save: "保存",
    share: "分享",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "打乱已复制",
    noSolvesToDelete: "没有可删除的还原",
    solveDeleted: "还原已删除",
    cannotAddPenaltyMBLD: "无法为MBLD还原添加罚时",
    dnfRemoved: "DNF已移除",
    dnfAdded: "DNF已添加",
    plus2Added: "+2罚时已添加",
    penaltyRemoved: "罚时已移除",
    newScrambleGenerated: "新打乱已生成",
    timesPanelOpened: "时间面板已打开",
  },
  times: {
    title: "还原时间",
    clear: "清除时间",
    close: "关闭",
    delete: "删除时间",
    confirmClear: "您确定要清除此项目的所有时间吗？",
    confirmDelete: "您确定要删除此时间吗？",
  },
  buttons: {
    viewTimes: "查看时间",
    ok: "确定",
    cancel: "取消",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3盲拧",
    "333fm": "3×3×3最少步",
    "333oh": "3×3×3单手",
    clock: "魔表",
    minx: "五魔方",
    pyram: "金字塔",
    skewb: "斜转",
    sq1: "SQ1",
    "444bf": "4×4×4盲拧",
    "555bf": "5×5×5盲拧",
    "333mbf": "3×3×3多盲",
  },
  mbld: {
    cubeCount: "魔方",
    solvedCount: "已还原魔方",
    totalCount: "魔方总数",
    totalCubes: "魔方总数",
    cubesSolved: "已还原魔方",
    bestPoints: "最佳分数",
    successRate: "成功率",
    points: "分",
    save: "保存结果",
    visualizations: "多盲可视化",
    scrambles: "多盲打乱",
    enterValidNumber: "请输入有效的已还原魔方数量。",
    noScrambles: "没有可用的MBLD打乱。请先选择3×3×3多盲项目。",
    visualizationNotFound: "未找到可视化模态框。请刷新页面并重试。",
    containerNotFound: "未找到可视化容器。请刷新页面并重试。",
    clickToView: "点击查看所有魔方可视化和打乱",
    bestScore: "最佳分数",
    worstScore: "最差分数",
    meanScore: "平均分数",
    averageScore: "平均分数",
    attempts: "次尝试",
    totalAttempts: "总尝试次数",
    clickToViewScrambles: "点击查看所有打乱",
    clickToViewScramblesCount: "点击查看所有{0}个打乱",
    setup: "多盲设置",
    results: "多盲结果",
    generateScrambles: "生成打乱",
    saveResult: "保存结果",
    cubeNumber: "魔方",
    numberOfCubesMinimum: "魔方数量（最少2个）：",
    numberOfCubesSolved: "已还原魔方数量：",
    saveFirst: "请先保存您的结果。",
    visualizationsTitle: "多盲可视化（{0}个魔方）",
    timeLimit: "时间限制：{0}分钟",
    timeLimitExceeded: "超过时间限制。结果将为DNF。",
    negativePoints: "负分。结果将为DNF。",
  },
  modals: {
    error: "错误",
    warning: "警告",
    info: "信息",
    confirm: "确认",
    prompt: "需要输入",
  },
  stackmat: {
    error: "Stackmat错误",
    noMicrophone: "启动Stackmat计时器失败：未找到麦克风。请连接麦克风并重试。",
    connected: "已连接",
    disconnected: "已断开",
    settingUp: "设置中...",
  },
  sessions: {
    newSessionTitle: "新会话",
    editSessionTitle: "编辑会话",
    sessionName: "会话名称：",
    sessionNamePlaceholder: "我的会话",
    puzzleType: "魔方类型：",
    create: "创建",
    save: "保存",
  },
  scramble: {
    loading: "加载打乱中...",
  },
  debug: {
    timerState: "计时器状态：",
    spaceHeldFor: "空格键保持时间：",
    currentEvent: "当前项目：",
    scrambleSource: "打乱来源：",
  },
  fmc: {
    title: "最少步挑战",
    info: "用尽可能少的步数还原魔方。您有60分钟找到解法。",
    timeRemaining: "剩余时间：",
    scramble: "打乱：",
    solution: "解法：",
    moveCount: "步数：",
    moves: "步",
    submit: "提交",
    resultTitle: "FMC结果",
    resultTime: "时间：",
    resultSolution: "解法：",
    resultOk: "确定",
    solutionPlaceholder: "在此使用标准WCA记号输入您的解法...",
    notationHelp: "记号帮助：",
    notationHelpContent:
      "面转：U、D、L、R、F、B（带'或2后缀）<br>宽转：Uw、Dw等<br>切层：M、E、S<br>整体转动：x、y、z（不计入总步数）",
    submitSolution: "提交解法",
    validSolution: "有效解法",
    invalidNotation: "检测到无效记号",
    bestMoves: "最少步数",
    worstMoves: "最多步数",
    meanMoves: "平均步数",
    bestMo3: "最佳mo3",
    averageMoves: "平均步数",
    attempts: "次尝试",
    totalAttempts: "总尝试次数",
    tooManyMoves: "解法超过80步限制",
    timeExceeded: "超过时间限制。如不提交，您的解法将标记为DNF。",
    confirmClose: "您确定要关闭吗？您的尝试将标记为DNF。",
    dnfReasonTimeout: "超过时间限制",
    dnfReasonInvalid: "无效记号",
    dnfReasonTooManyMoves: "解法超过80步",
    dnfReasonAbandoned: "尝试放弃",
    confirmSubmit: "您确定要提交解法吗？",
    pressToStart: "按空格键开始FMC尝试",
    solutionAccepted: "解法已接受",
    clickToViewTwizzle: "点击下方链接在Twizzle中查看解法",
    viewOnTwizzle: "在Twizzle中查看",
    moveCountLabel: "步数：",
    movesHTM: "步（HTM）",
    timeUsedLabel: "用时：",
    loadingFMC: "加载FMC中",
    generatingScramble: "生成打乱并准备界面",
  },
  tutorial: {
    welcomeTitle: "欢迎使用scTimer！",
    welcomeSubtitle: "您的专业速拧计时器",
    selectLanguage: "选择语言：",
    feature1: "WCA标准计时器",
    feature2: "高级统计",
    feature3: "所有WCA项目",
    feature4: "打乱生成器",
    welcomeDescription:
      "您想要快速了解如何有效使用scTimer吗？教程将在几个步骤中引导您了解主要功能。",
    skipTutorial: "跳过教程",
    startTour: "开始导览",
    step1: {
      title: "打乱显示",
      text: "这显示您当前魔方的打乱序列。每个打乱都按照WCA标准随机生成。",
    },
    step2: {
      title: "计时器控制",
      text: "按住空格键开始计时，松开开始还原。在移动端，点击并按住计时器区域。计时器遵循WCA检查标准。",
    },
    step3: {
      title: "项目选择器",
      text: "从所有WCA项目中选择，包括3x3x3、2x2x2、4x4x4和更多魔方类型。点击或轻触打开下拉菜单。",
    },
    step4: {
      title: "统计跟踪",
      text: "通过详细统计跟踪您的进步，包括最佳时间、5次、12次和100次平均。点击任何统计数据查看更多详情。",
    },
    step5: {
      title: "生成新打乱",
      text: "当您准备好下一次还原时生成新打乱。键盘快捷键：按N或点击打乱图标。",
    },
    step6: {
      title: "设置和自定义",
      text: "通过检查时间、声音选项、计时器模式和显示偏好自定义您的计时器体验。键盘快捷键：按S。",
    },
    step7: {
      title: "键盘快捷键",
      text: "掌握这些快捷键：空格键（开始/停止计时器）、N（新打乱）、S（设置）、ESC（关闭模态框）、方向键（导航）。在移动端使用滑动手势！",
    },
    step8: {
      title: "移动端手势",
      text: "在移动设备上：向左滑动打开时间面板，向右滑动关闭，点击并按住计时器开始，双击打乱复制。在可视化上捏合缩放。",
    },
    step9: {
      title: "专业提示和功能",
      text: "在设置中启用检查时间进行WCA练习。使用不同会话跟踪各种项目。导出您的时间进行分析。计时器作为PWA可离线工作！",
    },
    previous: "上一步",
    next: "下一步",
    finish: "完成",
    close: "关闭",
    stepCounter: "共",
    restartTutorial: "重新开始教程",
  },
};
