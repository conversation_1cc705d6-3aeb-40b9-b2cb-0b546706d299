// Kurdish Kurmanji (Northern Kurdish) language file
export default {
  dir: "ltr",
  settings: {
    title: "Mîhengî",
    save: "Tomar bike",
    close: "Bigire",
    language: "Ziman",
  },
  info: {
    title: "Agahî",
    pwaInstall: {
      title: "Wek Sepanê Saz bike",
      description:
        "scTimer wek Progressive Web App saz bike ji bo baştirîn ezmûnê. Bêhêl dixebite û wek sepana xwecî hîs dike.",
      install: "Sepanê Saz bike",
      iosTitle: "Sazkirin iOS/iPad:",
      iosStep1: "1. Li bişkoka Parvekirinê bitikîne",
      iosStep2:
        '2. Ber bi jêr ve here û li "Li Rûpela Sereke Zêde bike" bitikîne',
      iosStep3: '3. Ji bo sazkirin li "Zêde bike" bitikîne',
      note: "Di Chrome, Safari û gerokên nûjen de heye",
    },
    shortcuts: {
      title: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      timer: "Kontrolên Demjimêrê",
      spacebar: "Demjimêr dest pê bike/rawestîne",
      escape: "Kontrolê betal bike û modalên bigire",
      navigation: "Navîgasyon û Çalakî",
      generate: "Tevliheviya nû çê bike",
      list: "Lîsteya deman biguherîne",
      settings: "Mîhengan veke",
      edit: "Tevliheviya heyî sererast bike",
      copy: "Tevliheviyê li clipboard kopî bike",
      stats: "Statîstîkên berfireh veke",
      display: "Nîşandanê Biguherîne",
      visualization: "Dîtina puzzle biguherîne",
      statistics: "Nîşandana statîstîkan biguherîne",
      darkMode: "Moda tarî biguherîne",
      inspection: "Kontrola WCA biguherîne",
      learning: "Danegira algorîtmayan veke",
      penalties: "Rêveberiya Cezayan",
      removePenalty: "Ceza ji çareseriya dawî rake",
      addPlus2: "Cezaya +2 li çareseriya dawî zêde bike",
      addDNF: "Cezaya DNF li çareseriya dawî zêde bike",
      session: "Rêveberiya Danişînê",
      emptySession: "Danişîna heyî vala bike",
      exportSession: "Danişîna heyî derxe",
      eventSwitching: "Guhertina Bûyerê",
      alt2to7: "Berbi kûbên 2×2×2 heta 7×7×7 biçe",
      altP: "Berbi Pyraminx biçe",
      altM: "Berbi Megaminx biçe",
      altC: "Berbi Clock biçe",
      altS: "Berbi Skewb biçe",
      alt1: "Berbi Square-1 biçe",
      altF: "Berbi 3×3×3 Kêmtirîn Tevger biçe",
      altO: "Berbi 3×3×3 Yek Dest biçe",
      blindfolded: "Bûyerên Çavgirtî",
      altCtrl3: "Berbi 3×3×3 Çavgirtî biçe",
      altCtrl4: "Berbi 4×4×4 Çavgirtî biçe",
      altCtrl5: "Berbi 5×5×5 Çavgirtî biçe",
      altCtrl6: "Berbi 3×3×3 Pir-Çavgirtî biçe",
      sessionMgmt: "Rêveberiya Danişînê",
      altN: "Danişîna nû çê bike",
    },
    gestures: {
      title: "Tevgerên Mobîl",
      swipeDown: "Ber bi Jêr ve Bikişîne",
      swipeDownDesc: "Çareseriya dawî jê bibe",
      swipeUp: "Ber bi Jor ve Bikişîne",
      swipeUpDesc: "Cezayan biguherîne (tune/+2/DNF)",
      swipeLeft: "Ber bi Çepê ve Bikişîne",
      swipeLeftDesc: "LTR: Tevliheviya nû | RTL: Lîsteya deman",
      swipeRight: "Ber bi Rastê ve Bikişîne",
      swipeRightDesc: "LTR: Lîsteya deman | RTL: Tevliheviya nû",
      doubleClick: "Du Caran Bitikîne",
      doubleClickDesc: "Tevliheviya heyî kopî bike (PC/Mobîl)",
      longPress: "Dirêj Bitikîne/Bitikîne û Bigire",
      longPressDesc: "Tevliheviya heyî sererast bike (PC/Mobîl)",
    },
    learning: {
      title: "Danegira Algorîtmayan",
      description: "Algorîtmayên speedcubing fêr bibe û pratîk bike",
      puzzle: "Puzzle",
      method: "Rêbaz",
      step: "Gav",
      case: "Rewş",
      algorithm: "Algorîtma",
      alternatives: "Algorîtmayên Alternatîf",
      difficulty: "Zehmetî",
      moveCount: "Hejmara Tevgeran",
      category: "Kategorî",
      source: "Çavkanî",
      noAlgorithms: "Ji bo vê hilbijartinê algorîtma tune",
      loadingError: "Di barkirina danegira algorîtmayan de çewtî",
    },
    features: {
      title: "Taybetmendiyên Sereke",
      timer: "Demjimêra Pîşeyî",
      timerDesc: "Demjimêrîna lihevhatî bi WCA bi moda kontrolê",
      puzzles: "Hemû Bûyerên WCA",
      puzzlesDesc: "Piştgiriya tevahî ji bo hemû bûyerên fermî yên puzzle WCA",
      statistics: "Statîstîkên Pêşketî",
      statisticsDesc: "Analîzên berfireh bi ao5, ao12, ao100",
      scrambles: "Tevliheviyên Fermî",
      scramblesDesc: "Çêkirina tevliheviya standard WCA bi dîtina 2D",
      multilingual: "Piştgiriya Pirzimanî",
      multilingualDesc: "18+ ziman bi piştgiriya RTL",
      sync: "Hevdemkirina Google Drive",
      syncDesc: "Hevdemkirina navbera amûran bi tevlihevkirina aqilmend",
    },
    sync: {
      title: "Hevdemkirina Google Drive",
      description:
        "Demên çareseriya xwe li hemû amûran bi karanîna Google Drive hevdem bike. Daneyên te bi ewlehî di hesabê Google Drive yê takekesî de têne hilanîn.",
      secure: "Ewle û Taybet",
      automatic: "Hevdemkirina Otomatîk",
      offline: "Piştgiriya Bêhêl",
      smartMerge: "Tevlihevkirina Aqilmend",
      note: "Hevdemkirina Google Drive di Mîhengan de çalak bike da ku demên te li hemû amûrên te hevdem bin.",
    },
  },
  timerOptions: {
    title: "Vebijarkên Demjimêrê",
    warningSounds: "Dengên Hişyariyê Çalak bike",
    useInspection: "Kontrola WCA Bikar bîne (15ç)",
    inspectionSound: "Dengê Kontrolê:",
    inspectionSoundNone: "Tune",
    inspectionSoundVoice: "Deng",
    inspectionSoundBeep: "Bîp",
    stackmatResetInspection: "Reset Stackmat Kontrolê Çalak dike",
    stackmatResetNote: "Not: Tenê dema demjimêr li ser 0.000 nebe dixebite",
    inputTimer: "Moda Demjimêra Têketinê (Deman bi dest têxe)",
    timerMode: "Moda Demjimêrê:",
    timerModeTimer: "Demjimêr",
    timerModeTyping: "Nivîsandin",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "Bluetooth (Zû tê)",
    microphoneInput: "Têketina Mîkrofonê",
    microphoneAuto: "Naskirina otomatîk",
    microphoneNote: "Y-splitter an mîkrofona xwe ya derveyî hilbijêre",
    decimalPlaces: "Cihên Onî:",
    decimalPlacesNone: "Tune (12)",
    decimalPlaces1: "1 (12.3)",
    decimalPlaces2: "2 (12.34)",
    decimalPlaces3: "3 (12.345)",
  },
  displayOptions: {
    title: "Vebijarkên Nîşandanê",
    showVisualization: "Dîtina Puzzle Nîşan bide",
    showStats: "Statîstîkan Nîşan bide",
    showDebug: "Agahiyên Debug Nîşan bide",
    darkMode: "Moda Tarî",
    showFMCKeyboard: "Klavyeya FMC Nîşan bide",
    scrambleFontSize: "Mezinahiya Fontê ya Tevliheviyê",
  },
  app: {
    title: "scTimer",
    description: "Demjimêra speedcubing bi kontrola WCA û statîstîkan",
    enterTime: "Dem têxe",
    enterSolveTime: "Dema çareseriyê bi dest têxe",
    generateScrambles: "Tevlihevî Çê bike",
    outOf: "Ji:",
    numberOfCubes: "Hejmara kûban (kêmtirîn 2):",
    numberOfCubesSolved: "Hejmara kûbên çareser:",
  },
  timer: {
    ready: "Amade",
    running: "Dixebite",
    idle: "Bêkar",
    inspection: "Kontrol",
    holding: "Digire",
  },
  stats: {
    title: "Statîstîk",
    best: "Baştirîn",
    worst: "Xirabtrîn",
    mean: "Navîn",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "Baştirîn mo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "Çareserî",
    attempts: "Hewldan",
    moreStats: "Statîstîkên Zêdetir",
  },
  statsDetails: {
    title: "Hûrguliyên Statîstîkê",
    titleFor: "Hûrguliyên Statîstîkê ji bo",
    overview: "Dîtina Giştî",
    averages: "Navîn",
    records: "Rekor",
    timeDistribution: "Belavbûna Demê",
    progressChart: "Nexşeya Pêşveçûnê",
    sessionAnalysis: "Analîza Danişînê",
    predictions: "Pêşbînî",
    standardDeviation: "Xeletiya Standard",
    bestSingle: "Baştirîn Yekane",
    bestAo5: "Baştirîn ao5",
    bestAo12: "Baştirîn ao12",
    bestAo100: "Baştirîn ao100",
    bestAo1000: "Baştirîn ao1000",
    totalTime: "Dema Tevahî",
    averageTime: "Dema Navîn",
    solvesPerHour: "Çareserî/Saet",
    consistency: "Domdarî",
    nextAo5: "Armanca ao5 ya Pêş",
    nextAo12: "Armanca ao12 ya Pêş",
    improvementRate: "Rêjeya Baştirkirinê",
    targetTime: "Dema Armanc",
    currentSession: "Danişîna Heyî",
    allSessions: "Hemû Danişîn",
    importTimes: "Deman Têxe",
    exportJSON: "JSON Derxe",
    exportCSV: "CSV Derxe",
  },
  solveDetails: {
    title: "Hûrguliyên Çareseriyê",
    time: "Dem",
    date: "Dîrok",
    scramble: "Tevlihevî",
    editedScramble: "Tevliheviya Sererast",
    copyScramble: "Tevliheviyê kopî bike",
    penalty: "Ceza",
    none: "Tune",
    comment: "Şîrove",
    addComment: "Şîrove zêde bike...",
    save: "Tomar bike",
    share: "Parve bike",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "Tevlihevî kopî bû",
    noSolvesToDelete: "Çareserî tune ne ku werin jêbirin",
    solveDeleted: "Çareserî hate jêbirin",
    cannotAddPenaltyMBLD: "Nikare ceza li çareseriya MBLD zêde bike",
    dnfRemoved: "DNF hate rakirin",
    dnfAdded: "DNF hate zêdekirin",
    plus2Added: "Cezaya +2 hate zêdekirin",
    penaltyRemoved: "Ceza hate rakirin",
    newScrambleGenerated: "Tevliheviya nû hate çêkirin",
    timesPanelOpened: "Panela deman vebû",
  },
  times: {
    title: "Demên Çareseriyê",
    clear: "Deman Pak bike",
    close: "Bigire",
    delete: "Demê jê bibe",
    confirmClear: "Tu bawer î ku dixwazî hemû deman ji bo vê bûyerê pak bikî?",
    confirmDelete: "Tu bawer î ku dixwazî vê demê jê bibî?",
  },
  buttons: {
    viewTimes: "Deman Bibîne",
    ok: "Baş e",
    cancel: "Betal bike",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3 Çavgirtî",
    "333fm": "3×3×3 Kêmtirîn Tevger",
    "333oh": "3×3×3 Yek Dest",
    clock: "Clock",
    minx: "Megaminx",
    pyram: "Pyraminx",
    skewb: "Skewb",
    sq1: "Square-1",
    "444bf": "4×4×4 Çavgirtî",
    "555bf": "5×5×5 Çavgirtî",
    "333mbf": "3×3×3 Pir-Çavgirtî",
  },
  mbld: {
    cubeCount: "Kûb",
    solvedCount: "Kûbên Çareser",
    totalCount: "Tevahiya Kûban",
    totalCubes: "Tevahiya Kûban",
    cubesSolved: "Kûbên Çareser",
    bestPoints: "Baştirîn Puan",
    successRate: "Rêjeya Serkeftinê",
    points: "puan",
    save: "Encamê Tomar bike",
    visualizations: "Dîtinên Pir-Çavgirtî",
    scrambles: "Tevliheviyên Pir-Çavgirtî",
    enterValidNumber: "Ji kerema xwe hejmarek derbasdar a kûbên çareser têxe.",
    noScrambles:
      "Tevliheviyên MBLD tune ne. Ji kerema xwe pêşî bûyera 3×3×3 Pir-Çavgirtî hilbijêre.",
    visualizationNotFound:
      "Modala dîtinê nehat dîtin. Ji kerema xwe rûpelê nû bike û dîsa biceribîne.",
    containerNotFound:
      "Konteynerê dîtinê nehat dîtin. Ji kerema xwe rûpelê nû bike û dîsa biceribîne.",
    clickToView: "Ji bo dîtina hemû dîtin û tevliheviyên kûban bitikîne",
    bestScore: "Baştirîn Puan",
    worstScore: "Xirabtrîn Puan",
    meanScore: "Puana Navîn",
    averageScore: "Puana Navîn",
    attempts: "hewldan",
    totalAttempts: "Tevahiya Hewldanan",
    clickToViewScrambles: "Ji bo dîtina hemû tevlihevî bitikîne",
    clickToViewScramblesCount: "Ji bo dîtina hemû {0} tevlihevî bitikîne",
    setup: "Sazkirin Pir-Çavgirtî",
    results: "Encamên Pir-Çavgirtî",
    generateScrambles: "Tevlihevî Çê bike",
    saveResult: "Encamê Tomar bike",
    cubeNumber: "Kûb",
    numberOfCubesMinimum: "Hejmara kûban (kêmtirîn 2):",
    numberOfCubesSolved: "Hejmara kûbên çareser:",
    saveFirst: "Ji kerema xwe pêşî encama xwe tomar bike.",
    visualizationsTitle: "Dîtinên Pir-Çavgirtî ({0} kûb)",
    timeLimit: "Sînorê demê: {0} deqîqe",
    timeLimitExceeded: "Sînorê demê derbas bû. Encam dê DNF be.",
    negativePoints: "Puanên neyînî. Encam dê DNF be.",
  },
  modals: {
    error: "Xeletî",
    warning: "Hişyarî",
    info: "Agahî",
    confirm: "Bipejirîne",
    prompt: "Têketin Pêwîst e",
  },
  stackmat: {
    error: "Xeletiya Stackmat",
    noMicrophone:
      "Destpêkirina demjimêra Stackmat têk çû: Mîkrofon nehat dîtin. Ji kerema xwe mîkrofonek girêde û dîsa biceribîne.",
    connected: "Girêdayî",
    disconnected: "Veqetandî",
    settingUp: "Tê sazkirin...",
  },
  sessions: {
    newSessionTitle: "Danişîna Nû",
    editSessionTitle: "Danişînê Sererast bike",
    sessionName: "Navê Danişînê:",
    sessionNamePlaceholder: "Danişîna Min",
    puzzleType: "Cureyê Puzzle:",
    create: "Çê bike",
    save: "Tomar bike",
  },
  scramble: {
    loading: "Tevlihevî tê barkirin...",
  },
  debug: {
    timerState: "Rewşa Demjimêrê: ",
    spaceHeldFor: "Cih ji bo hatiye girtin: ",
    currentEvent: "Bûyera Heyî: ",
    scrambleSource: "Çavkaniya Tevliheviyê: ",
  },
  fmc: {
    title: "Şoreşa Kêmtirîn Tevger",
    info: "Kûbê bi kêmtirîn tevgerên gengaz çareser bike. 60 deqîqe ji bo dîtina çareseriyê hene.",
    timeRemaining: "Dema Mayî:",
    scramble: "Tevlihevî:",
    solution: "Çareserî:",
    moveCount: "Tevger:",
    moves: "tevger",
    submit: "Bişîne",
    resultTitle: "Encama FMC",
    resultTime: "Dem:",
    resultSolution: "Çareserî:",
    resultOk: "Baş e",
    solutionPlaceholder:
      "Çareseriya xwe li vir bi karanîna nîşana standard WCA têxe...",
    notationHelp: "Alîkariya Nîşanê:",
    notationHelpContent:
      "Zivirîna rûyan: U, D, L, R, F, B (bi paşgiran ' an 2)<br>Tevgerên fireh: Uw, Dw, hwd<br>Tevgerên parçeyan: M, E, S<br>Zivirîn: x, y, z (di tevahiya tevgeran de nayên jimartin)",
    submitSolution: "Çareseriyê Bişîne",
    validSolution: "Çareseriya derbasdar",
    invalidNotation: "Nîşana nederbasdar hat dîtin",
    bestMoves: "Baştirîn Tevger",
    worstMoves: "Xirabtrîn Tevger",
    meanMoves: "Tevgerên Navîn",
    bestMo3: "Baştirîn mo3",
    averageMoves: "Tevgerên Navîn",
    attempts: "hewldan",
    totalAttempts: "Tevahiya Hewldanan",
    tooManyMoves: "Çareserî ji sînorê 80 tevgeran derbas dibe",
    timeExceeded:
      "Sînorê demê derbas bû. Çareseriya te dê wek DNF were nîşankirin eger neyê şandin.",
    confirmClose:
      "Tu bawer î ku dixwazî bigirî? Hewldana te dê wek DNF were nîşankirin.",
    dnfReasonTimeout: "Sînorê demê derbas bû",
    dnfReasonInvalid: "Nîşana nederbasdar",
    dnfReasonTooManyMoves: "Çareserî ji 80 tevgeran derbas dibe",
    dnfReasonAbandoned: "Hewldan hate berdan",
    confirmSubmit: "Tu bawer î ku dixwazî çareseriya xwe bişînî?",
    pressToStart: "Ji bo destpêkirina hewldana FMC cih bitikîne",
    solutionAccepted: "Çareserî hate pejirandin",
    clickToViewTwizzle:
      "Li girêdana jêrîn bitikîne da ku çareseriyê di Twizzle de bibînî",
    viewOnTwizzle: "Di Twizzle de Bibîne",
    moveCountLabel: "Hejmara tevgeran:",
    movesHTM: "tevger (HTM)",
    timeUsedLabel: "Dema bikaranîn:",
    loadingFMC: "FMC tê barkirin",
    generatingScramble: "tevlihevî tê çêkirin û navrû tê amadekirin",
  },
  tutorial: {
    welcomeTitle: "Bi xêr hatî scTimer!",
    welcomeSubtitle: "Demjimêra speedcubing ya pîşeyî ya te",
    selectLanguage: "Ziman Hilbijêre:",
    feature1: "Demjimêra Standard WCA",
    feature2: "Statîstîkên Pêşketî",
    feature3: "Hemû Bûyerên WCA",
    feature4: "Çêkerê Tevliheviyê",
    welcomeDescription:
      "Tu dixwazî geştek bilez bikî da ku fêr bibî ka meriv çawa bi bandor scTimer bikar tîne? Hînkirin dê te di çend gavên hêsan de bi taybetmendiyên sereke re rêber bike.",
    skipTutorial: "Hînkirinê Derbas bike",
    startTour: "Geştê Dest pê bike",
    step1: {
      title: "Nîşandana Tevliheviyê",
      text: "Ev rêza tevliheviyê ji bo puzzle ya te ya heyî nîşan dide. Her tevlihevî li gorî standardên WCA bi rêkeftî tê çêkirin.",
    },
    step2: {
      title: "Kontrolên Demjimêrê",
      text: "Ji bo destpêkirina demjimêrînê CIHÊ bigire, ji bo destpêkirina çareseriyê berdin. Li mobîl, qada demjimêrê bigire û bigire. Demjimêr standardên kontrola WCA dişopîne.",
    },
    step3: {
      title: "Hilbijêrê Bûyerê",
      text: "Ji nav hemû bûyerên WCA yên di nav de 3x3x3, 2x2x2, 4x4x4 û gelek cureyên puzzle yên din hilbijêre. Ji bo vekirina menuya dakêşanê bitikîne an dest lê bide.",
    },
    step4: {
      title: "Şopandina Statîstîkan",
      text: "Pêşveçûna xwe bi statîstîkên berfireh yên di nav de demê baştirîn, navîna 5, 12 û 100 çareserî bişopîne. Ji bo dîtina hûrguliyên zêdetir li ser her statîstîkê bitikîne.",
    },
    step5: {
      title: "Çêkirina Tevliheviya Nû",
      text: "Dema ku ji bo çareseriya pêş amade bî tevliheviya nû çê bike. Kurteriya klavyeyê: N bitikîne an li îkona tevlihevkirinê bitikîne.",
    },
    step6: {
      title: "Mîheng û Kesayîkirin",
      text: "Ezmûna demjimêra xwe bi dema kontrolê, vebijarkên dengî, modên demjimêrê û mîhengên nîşandanê kesayî bike. Kurteriya klavyeyê: S bitikîne.",
    },
    step7: {
      title: "Kurteriyên Klavyeyê",
      text: "Van kurteriyan bike xwedî: CIH (demjimêr dest pê bike/rawestîne), N (tevliheviya nû), S (mîheng), ESC (modalan bigire), Tîr (navîgasyon). Li mobîl tevgerên kişandinê bikar bîne!",
    },
    step8: {
      title: "Tevgerên Mobîl",
      text: "Li amûrên mobîl: Ji bo vekirina panela deman ber bi çepê ve bikişîne, ji bo girtinê ber bi rastê ve bikişîne, ji bo destpêkirinê demjimêrê bigire û bigire, ji bo kopîkirinê du caran li tevliheviyê bitikîne. Ji bo mezinkirina dîtinan çimdik bike.",
    },
    step9: {
      title: "Şîret û Taybetmendiyên Pîşeyî",
      text: "Dema kontrolê di mîhengan de ji bo pratîka WCA çalak bike. Ji bo şopandina bûyerên cihêreng danişînên cihêreng bikar bîne. Demên xwe ji bo analîzê derxe. Demjimêr wek PWA bêhêl dixebite!",
    },
    previous: "Berê",
    next: "Pêş",
    finish: "Biqedîne",
    close: "Bigire",
    stepCounter: "ji",
    restartTutorial: "Hînkirinê Ji Nû ve Dest pê bike",
  },
};
