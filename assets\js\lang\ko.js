// Korean language file
export default {
  dir: "ltr",
  settings: {
    title: "설정",
    save: "저장",
    close: "닫기",
    language: "언어",
  },
  info: {
    title: "정보",
    pwaInstall: {
      title: "앱으로 설치",
      description:
        "최고의 경험을 위해 scTimer를 프로그레시브 웹 앱으로 설치하세요. 오프라인에서 작동하며 네이티브 앱처럼 느껴집니다.",
      install: "앱 설치",
      iosTitle: "iOS/iPad 설치:",
      iosStep1: "1. 공유 버튼 탭",
      iosStep2: '2. 아래로 스크롤하여 "홈 화면에 추가" 탭',
      iosStep3: '3. "추가"를 탭하여 설치',
      note: "Chrome, Safari 및 기타 최신 브라우저에서 사용 가능",
    },
    shortcuts: {
      title: "키보드 단축키",
      timer: "타이머 제어",
      spacebar: "타이머 시작/정지",
      escape: "검사 취소 및 모달 닫기",
      navigation: "탐색 및 작업",
      generate: "새 스크램블 생성",
      list: "시간 목록 토글",
      settings: "설정 열기",
      edit: "현재 스크램블 편집",
      copy: "스크램블을 클립보드에 복사",
      stats: "자세한 통계 열기",
      display: "디스플레이 토글",
      visualization: "퍼즐 시각화 토글",
      statistics: "통계 표시 토글",
      darkMode: "다크 모드 토글",
      inspection: "WCA 검사 토글",
      learning: "알고리즘 데이터베이스 열기",
      penalties: "페널티 관리",
      removePenalty: "최근 솔브에서 페널티 제거",
      addPlus2: "최근 솔브에 +2 페널티 추가",
      addDNF: "최근 솔브에 DNF 페널티 추가",
      session: "세션 관리",
      emptySession: "현재 세션 비우기",
      exportSession: "현재 세션 내보내기",
      eventSwitching: "이벤트 전환",
      alt2to7: "2×2×2에서 7×7×7 큐브로 전환",
      altP: "피라밍크스로 전환",
      altM: "메가밍크스로 전환",
      altC: "클락으로 전환",
      altS: "스큐브로 전환",
      alt1: "스퀘어-1로 전환",
      altF: "3×3×3 최소 이동으로 전환",
      altO: "3×3×3 한손으로 전환",
      blindfolded: "블라인드 이벤트",
      altCtrl3: "3×3×3 블라인드로 전환",
      altCtrl4: "4×4×4 블라인드로 전환",
      altCtrl5: "5×5×5 블라인드로 전환",
      altCtrl6: "3×3×3 멀티 블라인드로 전환",
      sessionMgmt: "세션 관리",
      altN: "새 세션 생성",
    },
    gestures: {
      title: "모바일 제스처",
      swipeDown: "아래로 스와이프",
      swipeDownDesc: "최근 솔브 삭제",
      swipeUp: "위로 스와이프",
      swipeUpDesc: "페널티 순환 (없음/+2/DNF)",
      swipeLeft: "왼쪽으로 스와이프",
      swipeLeftDesc: "LTR: 새 스크램블 | RTL: 시간 목록",
      swipeRight: "오른쪽으로 스와이프",
      swipeRightDesc: "LTR: 시간 목록 | RTL: 새 스크램블",
      doubleClick: "더블 클릭",
      doubleClickDesc: "현재 스크램블 복사 (PC/모바일)",
      longPress: "길게 누르기/클릭 앤 홀드",
      longPressDesc: "현재 스크램블 편집 (PC/모바일)",
    },
    learning: {
      title: "알고리즘 데이터베이스",
      description: "스피드큐빙 알고리즘 학습 및 연습",
      puzzle: "퍼즐",
      method: "방법",
      step: "단계",
      case: "케이스",
      algorithm: "알고리즘",
      alternatives: "대체 알고리즘",
      difficulty: "난이도",
      moveCount: "이동 횟수",
      category: "카테고리",
      source: "출처",
      noAlgorithms: "이 선택에 사용 가능한 알고리즘이 없습니다",
      loadingError: "알고리즘 데이터베이스 로딩 오류",
    },
    features: {
      title: "주요 기능",
      timer: "전문 타이머",
      timerDesc: "검사 모드가 있는 WCA 호환 타이밍",
      puzzles: "모든 WCA 이벤트",
      puzzlesDesc: "모든 공식 WCA 퍼즐 이벤트 완전 지원",
      statistics: "고급 통계",
      statisticsDesc: "ao5, ao12, ao100이 포함된 상세 분석",
      scrambles: "공식 스크램블",
      scramblesDesc: "2D 시각화가 있는 WCA 표준 스크램블 생성",
      multilingual: "다국어 지원",
      multilingualDesc: "RTL 지원이 있는 15+ 언어",
      sync: "Google Drive 동기화",
      syncDesc: "스마트 병합이 있는 크로스 디바이스 동기화",
    },
    sync: {
      title: "Google Drive 동기화",
      description:
        "Google Drive를 사용하여 모든 기기에서 솔브 시간을 동기화하세요. 데이터는 개인 Google Drive 계정에 안전하게 저장됩니다.",
      secure: "안전하고 비공개",
      automatic: "자동 동기화",
      offline: "오프라인 지원",
      smartMerge: "스마트 병합",
      note: "모든 기기에서 시간을 동기화하려면 설정에서 Google Drive 동기화를 활성화하세요.",
      status: "상태:",
      notConnected: "연결되지 않음",
      connected: "연결됨",
      connect: "연결",
      disconnect: "연결 해제",
      upload: "Drive에 업로드",
      download: "Drive에서 다운로드",
      autoSync: "자동 동기화",
      autoSyncNote: "인터넷에 연결되었을 때 자동으로 시간을 동기화합니다",
      uploading: "업로드 중...",
      downloading: "다운로드 중...",
      syncing: "동기화 중...",
      uploadSuccess: "업로드 성공",
      downloadSuccess: "다운로드 성공",
      uploadFailed: "업로드 실패",
      downloadFailed: "다운로드 실패",
      uploadConfirm: "로컬 시간을 Google Drive에 업로드하고 병합하시겠습니까?",
      downloadConfirm:
        "Google Drive에서 데이터를 다운로드하고 로컬 시간과 병합하시겠습니까?",
      downloadMergeConfirm:
        "Google Drive 데이터를 로컬 시간과 병합합니다. 계속하시겠습니까?",
      reloadConfirm: "변경 사항을 보려면 페이지를 새로고침하시겠습니까?",
      autoSyncEnabled: "자동 동기화 활성화됨",
      signInFailed: "로그인 실패",
      noSyncFile: "동기화 파일을 찾을 수 없음",
      noDataFound: "데이터를 찾을 수 없음",
      uploadCancelled: "업로드 취소됨",
      downloadCancelled: "다운로드 취소됨",
      syncSuccessful: "동기화 성공",
      syncFailed: "동기화 실패",
      error: "오류",
    },
  },
  timerOptions: {
    title: "타이머 옵션",
    warningSounds: "경고음 활성화",
    useInspection: "WCA 검사 사용 (15초)",
    inspectionSound: "검사 소리:",
    inspectionSoundNone: "없음",
    inspectionSoundVoice: "음성",
    inspectionSoundBeep: "비프음",
    stackmatResetInspection: "Stackmat 리셋이 검사 시작",
    stackmatResetNote: "참고: 타이머가 0.000이 아닐 때만 작동",
    inputTimer: "입력 타이머 모드 (시간 수동 입력)",
    timerMode: "타이머 모드:",
    timerModeTimer: "타이머",
    timerModeTyping: "입력",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "블루투스 (곧 출시)",
    microphoneInput: "마이크 입력",
    microphoneAuto: "자동 감지",
    microphoneNote: "Y 스플리터 또는 외부 마이크 선택",
    decimalPlaces: "소수점 자릿수:",
    decimalPlacesNone: "없음 (12)",
    decimalPlaces1: "1자리 (12.3)",
    decimalPlaces2: "2자리 (12.34)",
    decimalPlaces3: "3자리 (12.345)",
  },
  displayOptions: {
    title: "디스플레이 옵션",
    showVisualization: "퍼즐 시각화 표시",
    showStats: "통계 표시",
    showDebug: "디버그 정보 표시",
    darkMode: "다크 모드",
    showFMCKeyboard: "FMC 키보드 표시",
    scrambleFontSize: "스크램블 글꼴 크기",
  },
  app: {
    title: "scTimer",
    description: "WCA 검사 및 통계가 있는 스피드큐빙 타이머",
    enterTime: "시간 입력",
    enterSolveTime: "솔브 시간 수동 입력",
    generateScrambles: "스크램블 생성",
    outOf: "총:",
    numberOfCubes: "큐브 수 (최소 2개):",
    numberOfCubesSolved: "해결된 큐브 수:",
  },
  timer: {
    ready: "준비",
    running: "실행 중",
    idle: "대기",
    inspection: "검사",
    holding: "홀딩",
  },
  stats: {
    title: "통계",
    best: "최고",
    worst: "최악",
    mean: "평균",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "최고 mo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "솔브",
    attempts: "시도",
    moreStats: "더 많은 통계",
  },
  statsDetails: {
    title: "통계 세부사항",
    titleFor: "통계 세부사항",
    overview: "개요",
    averages: "평균",
    records: "기록",
    timeDistribution: "시간 분포",
    progressChart: "진행 차트",
    sessionAnalysis: "세션 분석",
    predictions: "예측",
    standardDeviation: "표준편차",
    bestSingle: "최고 싱글",
    bestAo5: "최고 ao5",
    bestAo12: "최고 ao12",
    bestAo100: "최고 ao100",
    bestAo1000: "최고 ao1000",
    totalTime: "총 시간",
    averageTime: "평균 시간",
    solvesPerHour: "시간당 솔브",
    consistency: "일관성",
    nextAo5: "다음 ao5 목표",
    nextAo12: "다음 ao12 목표",
    improvementRate: "개선율",
    targetTime: "목표 시간",
    currentSession: "현재 세션",
    allSessions: "모든 세션",
    importTimes: "시간 가져오기",
    exportJSON: "JSON 내보내기",
    exportCSV: "CSV 내보내기",
  },
  solveDetails: {
    title: "솔브 세부사항",
    time: "시간",
    date: "날짜",
    scramble: "스크램블",
    editedScramble: "편집된 스크램블",
    copyScramble: "스크램블 복사",
    penalty: "페널티",
    none: "없음",
    comment: "댓글",
    addComment: "댓글 추가...",
    save: "저장",
    share: "공유",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "스크램블 복사됨",
    noSolvesToDelete: "삭제할 솔브 없음",
    solveDeleted: "솔브 삭제됨",
    cannotAddPenaltyMBLD: "MBLD 솔브에 페널티 추가 불가",
    dnfRemoved: "DNF 제거됨",
    dnfAdded: "DNF 추가됨",
    plus2Added: "+2 페널티 추가됨",
    penaltyRemoved: "페널티 제거됨",
    newScrambleGenerated: "새 스크램블 생성됨",
    timesPanelOpened: "시간 패널 열림",
  },
  times: {
    title: "솔브 시간",
    clear: "시간 지우기",
    close: "닫기",
    delete: "시간 삭제",
    confirmClear: "이 이벤트의 모든 시간을 지우시겠습니까?",
    confirmDelete: "이 시간을 삭제하시겠습니까?",
  },
  buttons: {
    viewTimes: "시간 보기",
    ok: "확인",
    cancel: "취소",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3 블라인드",
    "333fm": "3×3×3 최소 이동",
    "333oh": "3×3×3 한손",
    clock: "클락",
    minx: "메가밍크스",
    pyram: "피라밍크스",
    skewb: "스큐브",
    sq1: "스퀘어-1",
    "444bf": "4×4×4 블라인드",
    "555bf": "5×5×5 블라인드",
    "333mbf": "3×3×3 멀티 블라인드",
  },
  mbld: {
    cubeCount: "큐브",
    solvedCount: "해결된 큐브",
    totalCount: "총 큐브 수",
    totalCubes: "총 큐브 수",
    cubesSolved: "해결된 큐브",
    bestPoints: "최고 점수",
    successRate: "성공률",
    points: "포인트",
    save: "결과 저장",
    visualizations: "멀티 블라인드 시각화",
    scrambles: "멀티 블라인드 스크램블",
    enterValidNumber: "유효한 해결된 큐브 수를 입력하세요.",
    noScrambles:
      "사용 가능한 MBLD 스크램블이 없습니다. 먼저 3×3×3 멀티 블라인드 이벤트를 선택하세요.",
    visualizationNotFound:
      "시각화 모달을 찾을 수 없습니다. 페이지를 새로고침하고 다시 시도하세요.",
    containerNotFound:
      "시각화 컨테이너를 찾을 수 없습니다. 페이지를 새로고침하고 다시 시도하세요.",
    clickToView: "모든 큐브 시각화와 스크램블을 보려면 클릭하세요",
    bestScore: "최고 점수",
    worstScore: "최악 점수",
    meanScore: "평균 점수",
    averageScore: "평균 점수",
    attempts: "시도",
    totalAttempts: "총 시도 수",
    clickToViewScrambles: "모든 스크램블을 보려면 클릭하세요",
    clickToViewScramblesCount: "모든 {0}개 스크램블을 보려면 클릭하세요",
    setup: "멀티 블라인드 설정",
    results: "멀티 블라인드 결과",
    generateScrambles: "스크램블 생성",
    saveResult: "결과 저장",
    cubeNumber: "큐브",
    numberOfCubesMinimum: "큐브 수 (최소 2개):",
    numberOfCubesSolved: "해결된 큐브 수:",
    saveFirst: "먼저 결과를 저장하세요.",
    visualizationsTitle: "멀티 블라인드 시각화 ({0}개 큐브)",
    timeLimit: "시간 제한: {0}분",
    timeLimitExceeded: "시간 제한을 초과했습니다. 결과는 DNF가 됩니다.",
    negativePoints: "음수 점수입니다. 결과는 DNF가 됩니다.",
  },
  modals: {
    error: "오류",
    warning: "경고",
    info: "정보",
    confirm: "확인",
    prompt: "입력 필요",
  },
  stackmat: {
    error: "Stackmat 오류",
    noMicrophone:
      "Stackmat 타이머 시작 실패: 마이크를 찾을 수 없습니다. 마이크를 연결하고 다시 시도하세요.",
    connected: "연결됨",
    disconnected: "연결 해제됨",
    settingUp: "설정 중...",
  },
  sessions: {
    newSessionTitle: "새 세션",
    editSessionTitle: "세션 편집",
    sessionName: "세션 이름:",
    sessionNamePlaceholder: "내 세션",
    puzzleType: "퍼즐 유형:",
    create: "생성",
    save: "저장",
  },
  scramble: {
    loading: "스크램블 로딩 중...",
  },
  debug: {
    timerState: "타이머 상태: ",
    spaceHeldFor: "스페이스 키 누른 시간: ",
    currentEvent: "현재 이벤트: ",
    scrambleSource: "스크램블 소스: ",
  },
  fmc: {
    title: "최소 이동 챌린지",
    info: "가능한 한 적은 이동으로 큐브를 해결하세요. 해결책을 찾는 데 60분이 주어집니다.",
    timeRemaining: "남은 시간:",
    scramble: "스크램블:",
    solution: "해결책:",
    moveCount: "이동 수:",
    moves: "이동",
    submit: "제출",
    resultTitle: "FMC 결과",
    resultTime: "시간:",
    resultSolution: "해결책:",
    resultOk: "확인",
    solutionPlaceholder:
      "표준 WCA 표기법을 사용하여 여기에 해결책을 입력하세요...",
    notationHelp: "표기법 도움말:",
    notationHelpContent:
      "면 회전: U, D, L, R, F, B (' 또는 2 접미사 포함)<br>와이드 이동: Uw, Dw 등<br>슬라이스 이동: M, E, S<br>회전: x, y, z (총 이동 수에 포함되지 않음)",
    submitSolution: "해결책 제출",
    validSolution: "유효한 해결책",
    invalidNotation: "잘못된 표기법이 감지되었습니다",
    bestMoves: "최고 이동 수",
    worstMoves: "최악 이동 수",
    meanMoves: "평균 이동 수",
    bestMo3: "최고 mo3",
    averageMoves: "평균 이동 수",
    attempts: "시도",
    totalAttempts: "총 시도 수",
    tooManyMoves: "해결책이 80이동 제한을 초과합니다",
    timeExceeded:
      "시간 제한을 초과했습니다. 제출하지 않으면 해결책이 DNF로 표시됩니다.",
    confirmClose: "정말 닫으시겠습니까? 시도가 DNF로 표시됩니다.",
    dnfReasonTimeout: "시간 제한 초과",
    dnfReasonInvalid: "잘못된 표기법",
    dnfReasonTooManyMoves: "해결책이 80이동을 초과함",
    dnfReasonAbandoned: "시도 포기",
    confirmSubmit: "정말 해결책을 제출하시겠습니까?",
    pressToStart: "스페이스 키를 눌러 FMC 시도를 시작하세요",
    solutionAccepted: "해결책이 수락되었습니다",
    clickToViewTwizzle: "아래 링크를 클릭하여 Twizzle에서 해결책을 보세요",
    viewOnTwizzle: "Twizzle에서 보기",
    moveCountLabel: "이동 수:",
    movesHTM: "이동 (HTM)",
    timeUsedLabel: "사용 시간:",
    loadingFMC: "FMC 로딩 중",
    generatingScramble: "스크램블 생성 및 인터페이스 준비 중",
  },
  tutorial: {
    welcomeTitle: "scTimer에 오신 것을 환영합니다!",
    welcomeSubtitle: "전문 스피드큐빙 타이머",
    selectLanguage: "언어 선택:",
    feature1: "WCA 표준 타이머",
    feature2: "고급 통계",
    feature3: "모든 WCA 이벤트",
    feature4: "스크램블 생성기",
    welcomeDescription:
      "scTimer를 효과적으로 사용하는 방법을 배우는 빠른 투어를 원하시나요? 튜토리얼이 몇 단계만으로 주요 기능을 안내해드립니다.",
    skipTutorial: "튜토리얼 건너뛰기",
    startTour: "투어 시작",
    step1: {
      title: "스크램블 표시",
      text: "현재 퍼즐의 스크램블 시퀀스를 보여줍니다. 각 스크램블은 WCA 표준에 따라 무작위로 생성됩니다.",
    },
    step2: {
      title: "타이머 제어",
      text: "스페이스바를 눌러 타이밍을 시작하고, 놓으면 해결을 시작합니다. 모바일에서는 타이머 영역을 탭하고 홀드하세요. 타이머는 WCA 검사 표준을 따릅니다.",
    },
    step3: {
      title: "이벤트 선택기",
      text: "3x3x3, 2x2x2, 4x4x4 및 기타 여러 퍼즐 유형을 포함한 모든 WCA 이벤트 중에서 선택하세요. 클릭하거나 탭하여 드롭다운 메뉴를 여세요.",
    },
    step4: {
      title: "통계 추적",
      text: "최고 시간, 5회, 12회, 100회 평균을 포함한 상세한 통계로 진행 상황을 추적하세요. 통계를 클릭하면 더 자세한 내용을 볼 수 있습니다.",
    },
    step5: {
      title: "새 스크램블 생성",
      text: "다음 솔브 준비가 되면 새 스크램블을 생성하세요. 키보드 단축키: N을 누르거나 셔플 아이콘을 클릭하세요.",
    },
    step6: {
      title: "설정 및 사용자 정의",
      text: "검사 시간, 사운드 옵션, 타이머 모드, 디스플레이 설정으로 타이머 경험을 사용자 정의하세요. 키보드 단축키: S를 누르세요.",
    },
    step7: {
      title: "키보드 단축키",
      text: "이러한 단축키를 마스터하세요: 스페이스바 (타이머 시작/정지), N (새 스크램블), S (설정), ESC (모달 닫기), 화살표 키 (탐색). 모바일에서는 스와이프 제스처를 사용하세요!",
    },
    step8: {
      title: "모바일 제스처",
      text: "모바일 기기에서: 왼쪽으로 스와이프하여 시간 패널 열기, 오른쪽으로 스와이프하여 닫기, 타이머를 탭하고 홀드하여 시작, 스크램블을 더블 탭하여 복사. 시각화에서 핀치하여 확대/축소하세요.",
    },
    step9: {
      title: "프로 팁 및 기능",
      text: "WCA 연습을 위해 설정에서 검사 시간을 활성화하세요. 다양한 이벤트를 추적하기 위해 다른 세션을 사용하세요. 분석을 위해 시간을 내보내세요. 타이머는 PWA로 오프라인에서 작동합니다!",
    },
    previous: "이전",
    next: "다음",
    finish: "완료",
    close: "닫기",
    stepCounter: "의",
    restartTutorial: "튜토리얼 다시 시작",
  },
};
